<?php
/**
 * CRON ЗАДАЧА ДЛЯ МОНИТОРИНГА ЦЕЛОСТНОСТИ ДАННЫХ
 * Запускается каждые 5 минут для проверки целостности user_data.json
 */

require_once __DIR__ . '/../data_integrity_monitor.php';

// Логируем запуск
error_log("CRON: Запуск проверки целостности данных");

try {
    $monitor = new DataIntegrityMonitor();
    $results = $monitor->performIntegrityCheck();
    
    // Логируем результат
    if ($results['status'] === 'OK') {
        error_log("CRON: Проверка целостности данных - OK");
    } else {
        error_log("CRON: ПРОБЛЕМА с целостностью данных - " . $results['status']);
        
        // Отправляем детальную информацию в лог
        foreach ($results['checks'] as $check) {
            if ($check['status'] !== 'OK') {
                error_log("CRON: {$check['name']}: {$check['status']} - {$check['message']}");
            }
        }
    }
    
} catch (Exception $e) {
    error_log("CRON ERROR: Ошибка при проверке целостности данных: " . $e->getMessage());
}

?>
