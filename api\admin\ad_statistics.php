<?php
// Подключаем аутентификацию и шаблоны
require_once __DIR__ . '/auth.php';
session_start();
if (!isAuthenticated()) {
    header('Location: login.php');
    exit;
}
include 'templates/header.php';
?>

<style>
    .chart-container {
        position: relative;
        height: 500px;
        width: 100%;
        min-height: 400px;
    }

    .card-body canvas {
        min-height: 400px !important;
        height: 500px !important;
    }

    .hourly-chart-container {
        height: 450px;
        min-height: 400px;
    }

    .types-chart-container {
        height: 500px;
        min-height: 450px;
    }
</style>

<div class="container-fluid">
    <div class="row">
        <?php include 'templates/sidebar.php'; ?>

        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">Статистика рекламы</h1>
            </div>

            <!-- Фильтры -->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="row g-3 align-items-end">
                        <div class="col-md-3">
                            <label for="date-from" class="form-label">Дата от</label>
                            <input type="date" id="date-from" class="form-control">
                        </div>
                        <div class="col-md-3">
                            <label for="date-to" class="form-label">Дата до</label>
                            <input type="date" id="date-to" class="form-control">
                        </div>
                        <div class="col-md-3">
                            <label for="ad-type" class="form-label">Тип рекламы</label>
                            <select id="ad-type" class="form-select">
                                <option value="all">Все типы</option>
                                <option value="native_banner">native_banner (автопереход)</option>
                                <option value="rewarded_video">rewarded_video</option>
                                <option value="interstitial">native_banner</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <button id="apply-filters" class="btn btn-primary w-100">Применить</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Графики -->
            <div class="row">
                <div class="col-lg-8 mb-4">
                    <div class="card">
                        <div class="card-header">Статистика по типам</div>
                        <div class="card-body types-chart-container">
                            <canvas id="types-chart"></canvas>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4 mb-4">
                    <div class="card">
                        <div class="card-header">Активность по часам (UTC)</div>
                        <div class="card-body hourly-chart-container">
                            <canvas id="hourly-chart"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Таблицы -->
            <div class="row">
                <div class="col-lg-8 mb-4">
                    <div class="card">
                        <div class="card-header">Детализированная статистика</div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Тип рекламы</th>
                                            <th>Клики</th>
                                            <th>Просмотры</th>
                                            <th>CTR (%)</th>
                                            <th>Заработано</th>
                                        </tr>
                                    </thead>
                                    <tbody id="stats-table-body"></tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4 mb-4">
                    <div class="card">
                        <div class="card-header">Статистика по странам</div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Страна</th>
                                            <th>Клики</th>
                                        </tr>
                                    </thead>
                                    <tbody id="country-stats-body"></tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    let typesChart, hourlyChart;

    const applyFiltersBtn = document.getElementById('apply-filters');

    function fetchData() {
        const dateFrom = document.getElementById('date-from').value;
        const dateTo = document.getElementById('date-to').value;
        const adType = document.getElementById('ad-type').value;

        const params = new URLSearchParams({
            date_from: dateFrom,
            date_to: dateTo,
            ad_type: adType
        });

        applyFiltersBtn.disabled = true;
        applyFiltersBtn.textContent = 'Загрузка...';

        fetch(`ad_stats_api.php?${params}`)
            .then(response => {
                if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    renderAll(data);
                } else {
                    throw new Error(data.error || 'API returned an error');
                }
            })
            .catch(error => {
                console.error('Fetch error:', error);
                alert('Не удалось загрузить статистику. Проверьте консоль (F12) для деталей.');
            })
            .finally(() => {
                applyFiltersBtn.disabled = false;
                applyFiltersBtn.textContent = 'Применить';
            });
    }

    function renderAll(data) {
        renderTable(data.stats_by_type);
        renderCountryTable(data.stats_by_country);
        renderTypesChart(data.stats_by_type);
        renderHourlyChart(data.hourly_stats);
    }

    // Функция для преобразования технических названий типов рекламы в пользовательские
    function getAdTypeDisplayName(adType) {
        const adTypeNames = {
            'native_banner': 'native_banner (автопереход)',
            'rewarded_video': 'rewarded_video',
            'interstitial': 'native_banner'
        };
        return adTypeNames[adType] || adType;
    }

    function renderTable(stats) {
        const tableBody = document.getElementById('stats-table-body');
        tableBody.innerHTML = '';
        if (Object.keys(stats).length === 0) {
            tableBody.innerHTML = '<tr><td colspan="5" class="text-center">Нет данных за выбранный период.</td></tr>';
            return;
        }
        for (const adType in stats) {
            const data = stats[adType];
            tableBody.innerHTML += `
                <tr>
                    <td>${getAdTypeDisplayName(adType)}</td>
                    <td>${data.clicks}</td>
                    <td>${data.views}</td>
                    <td>${data.ctr}%</td>
                    <td>${data.rewards}</td>
                </tr>`;
        }
    }

    function renderCountryTable(stats) {
        const tableBody = document.getElementById('country-stats-body');
        tableBody.innerHTML = '';
        if (Object.keys(stats).length === 0) {
            tableBody.innerHTML = '<tr><td colspan="2" class="text-center">Нет данных.</td></tr>';
            return;
        }
        for (const country in stats) {
            tableBody.innerHTML += `
                <tr>
                    <td>${country}</td>
                    <td>${stats[country]}</td>
                </tr>`;
        }
    }

    function renderTypesChart(stats) {
        const ctx = document.getElementById('types-chart').getContext('2d');
        const labels = Object.keys(stats);
        const displayLabels = labels.map(label => getAdTypeDisplayName(label));
        const chartData = {
            labels: displayLabels,
            datasets: [
                { label: 'Клики', data: labels.map(l => stats[l].clicks), backgroundColor: 'rgba(54, 162, 235, 0.6)' },
                { label: 'Просмотры', data: labels.map(l => stats[l].views), backgroundColor: 'rgba(75, 192, 192, 0.6)' }
            ]
        };
        if (typesChart) typesChart.destroy();
        typesChart = new Chart(ctx, {
            type: 'bar',
            data: chartData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Количество'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Типы рекламы'
                        }
                    }
                },
                plugins: {
                    legend: {
                        position: 'top'
                    }
                }
            }
        });
    }

    function renderHourlyChart(stats) {
        const ctx = document.getElementById('hourly-chart').getContext('2d');

        // Получаем текущее UTC время
        const currentUTC = new Date();
        const currentUTCHour = currentUTC.getUTCHours();

        // Создаем метки времени с указанием UTC и выделением текущего часа
        const labels = Object.keys(stats).map(h => {
            const hour = parseInt(h);
            const isCurrentHour = hour === currentUTCHour;
            return `${hour.toString().padStart(2, '0')}:00${isCurrentHour ? ' ⭐' : ''}`;
        });

        // Создаем данные с выделением текущего часа
        const clicksData = Object.values(stats).map(s => s.clicks);
        const viewsData = Object.values(stats).map(s => s.views);

        const chartData = {
            labels: labels,
            datasets: [
                {
                    label: 'Клики',
                    data: clicksData,
                    borderColor: 'rgba(255, 99, 132, 1)',
                    backgroundColor: 'rgba(255, 99, 132, 0.1)',
                    tension: 0.1,
                    fill: false,
                    pointBackgroundColor: Object.keys(stats).map((h, i) =>
                        parseInt(h) === currentUTCHour ? 'rgba(255, 99, 132, 1)' : 'rgba(255, 99, 132, 0.7)'
                    ),
                    pointRadius: Object.keys(stats).map((h, i) =>
                        parseInt(h) === currentUTCHour ? 6 : 3
                    )
                },
                {
                    label: 'Просмотры',
                    data: viewsData,
                    borderColor: 'rgba(255, 159, 64, 1)',
                    backgroundColor: 'rgba(255, 159, 64, 0.1)',
                    tension: 0.1,
                    fill: false,
                    pointBackgroundColor: Object.keys(stats).map((h, i) =>
                        parseInt(h) === currentUTCHour ? 'rgba(255, 159, 64, 1)' : 'rgba(255, 159, 64, 0.7)'
                    ),
                    pointRadius: Object.keys(stats).map((h, i) =>
                        parseInt(h) === currentUTCHour ? 6 : 3
                    )
                }
            ]
        };

        if (hourlyChart) hourlyChart.destroy();
        hourlyChart = new Chart(ctx, {
            type: 'line',
            data: chartData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                interaction: {
                    intersect: false,
                    mode: 'index'
                },
                scales: {
                    x: {
                        title: {
                            display: true,
                            text: `Время (UTC) - Сейчас: ${currentUTC.getUTCHours().toString().padStart(2, '0')}:${currentUTC.getUTCMinutes().toString().padStart(2, '0')} UTC`
                        },
                        ticks: {
                            maxRotation: 45,
                            minRotation: 45
                        },
                        grid: {
                            color: function(context) {
                                const hour = parseInt(Object.keys(stats)[context.index] || 0);
                                return hour === currentUTCHour ? 'rgba(255, 193, 7, 0.8)' : 'rgba(0, 0, 0, 0.1)';
                            },
                            lineWidth: function(context) {
                                const hour = parseInt(Object.keys(stats)[context.index] || 0);
                                return hour === currentUTCHour ? 2 : 1;
                            }
                        }
                    },
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Количество'
                        }
                    }
                },
                plugins: {
                    tooltip: {
                        callbacks: {
                            title: function(context) {
                                const hour = Object.keys(stats)[context[0].dataIndex];
                                const isCurrentHour = parseInt(hour) === currentUTCHour;
                                return `${hour.padStart(2, '0')}:00 UTC${isCurrentHour ? ' (текущий час)' : ''}`;
                            },
                            afterTitle: function(context) {
                                const hour = parseInt(Object.keys(stats)[context[0].dataIndex]);
                                if (hour === currentUTCHour) {
                                    return `Текущее время: ${currentUTC.getUTCHours().toString().padStart(2, '0')}:${currentUTC.getUTCMinutes().toString().padStart(2, '0')} UTC`;
                                }
                                return '';
                            }
                        }
                    },
                    legend: {
                        labels: {
                            generateLabels: function(chart) {
                                const original = Chart.defaults.plugins.legend.labels.generateLabels;
                                const labels = original.call(this, chart);
                                labels.push({
                                    text: `⭐ Текущий час (${currentUTCHour.toString().padStart(2, '0')}:00 UTC)`,
                                    fillStyle: 'rgba(255, 193, 7, 0.8)',
                                    strokeStyle: 'rgba(255, 193, 7, 1)',
                                    lineWidth: 2
                                });
                                return labels;
                            }
                        }
                    }
                }
            }
        });
    }

    applyFiltersBtn.addEventListener('click', fetchData);

    // Initial data load
    fetchData();
});
</script>

<?php
include 'templates/footer.php';
?>
