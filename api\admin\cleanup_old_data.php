<?php
/**
 * Скрипт для очистки старых данных статистики
 * Удаляет записи старше 30 дней для улучшения производительности
 */

// Подключаем аутентификацию
require_once __DIR__ . '/auth.php';
session_start();
if (!isAuthenticated()) {
    http_response_code(403);
    echo json_encode(['success' => false, 'error' => 'Unauthorized']);
    exit;
}

header('Content-Type: application/json');

function cleanupOldData($filepath, $days = 30) {
    if (!file_exists($filepath)) {
        return ['cleaned' => 0, 'total' => 0, 'error' => 'File not found'];
    }
    
    $content = file_get_contents($filepath);
    if (empty($content)) {
        return ['cleaned' => 0, 'total' => 0, 'error' => 'Empty file'];
    }
    
    $data = json_decode($content, true);
    if (json_last_error() !== JSON_ERROR_NONE) {
        return ['cleaned' => 0, 'total' => 0, 'error' => 'JSON decode error: ' . json_last_error_msg()];
    }
    
    if (!is_array($data)) {
        return ['cleaned' => 0, 'total' => 0, 'error' => 'Invalid data format'];
    }
    
    $total_count = count($data);
    $cutoff_date = date('Y-m-d H:i:s', strtotime("-{$days} days"));
    
    // Фильтруем записи, оставляем только свежие
    $filtered_data = array_filter($data, function($item) use ($cutoff_date) {
        if (!isset($item['timestamp'])) return false;
        return $item['timestamp'] >= $cutoff_date;
    });
    
    $cleaned_count = $total_count - count($filtered_data);
    
    if ($cleaned_count > 0) {
        // Создаем резервную копию
        $backup_file = $filepath . '.backup.' . date('Y-m-d_H-i-s');
        file_put_contents($backup_file, $content);
        
        // Сохраняем очищенные данные
        $result = file_put_contents($filepath, json_encode(array_values($filtered_data), JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
        
        if ($result === false) {
            return ['cleaned' => 0, 'total' => $total_count, 'error' => 'Failed to write cleaned data'];
        }
    }
    
    return [
        'cleaned' => $cleaned_count,
        'total' => $total_count,
        'remaining' => count($filtered_data),
        'backup_created' => $cleaned_count > 0 ? basename($backup_file) : null
    ];
}

function cleanupCacheFiles() {
    $cache_dir = __DIR__ . '/../../database/';
    $cache_files = glob($cache_dir . 'stats_cache_*.json');
    $cleaned = 0;
    
    foreach ($cache_files as $file) {
        if (time() - filemtime($file) > 3600) { // Старше 1 часа
            unlink($file);
            $cleaned++;
        }
    }
    
    return $cleaned;
}

try {
    $days = isset($_GET['days']) ? (int)$_GET['days'] : 30;
    if ($days < 1 || $days > 365) {
        throw new Exception('Invalid days parameter. Must be between 1 and 365.');
    }
    
    $results = [];
    
    // Очищаем файлы данных
    $files_to_clean = [
        'ad_clicks.json' => 'Клики по рекламе',
        'ad_views.json' => 'Просмотры рекламы'
    ];
    
    foreach ($files_to_clean as $filename => $description) {
        $filepath = __DIR__ . '/../../database/' . $filename;
        $result = cleanupOldData($filepath, $days);
        $result['file'] = $filename;
        $result['description'] = $description;
        $results[] = $result;
    }
    
    // Очищаем кэш файлы
    $cache_cleaned = cleanupCacheFiles();
    
    $response = [
        'success' => true,
        'days' => $days,
        'files' => $results,
        'cache_files_cleaned' => $cache_cleaned,
        'timestamp' => date('Y-m-d H:i:s')
    ];
    
} catch (Exception $e) {
    $response = [
        'success' => false,
        'error' => $e->getMessage()
    ];
}

echo json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
