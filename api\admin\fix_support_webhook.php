<?php
/**
 * Скрипт для исправления webhook бота поддержки
 * Автоматически обновляет webhook на основе текущих настроек
 */

// Подключаем аутентификацию
require_once __DIR__ . '/auth.php';
session_start();
if (!isAuthenticated()) {
    http_response_code(403);
    echo json_encode(['success' => false, 'error' => 'Unauthorized']);
    exit;
}

// Подключаем конфигурацию
require_once __DIR__ . '/../../includes/bot_config_loader.php';
defineBotConstants();

header('Content-Type: application/json');

/**
 * Функция для обновления webhook бота поддержки
 */
function updateSupportWebhook($supportWebhookUrl, $supportBotToken) {
    try {
        // Отправляем запрос на обновление webhook бота поддержки
        $url = "https://api.telegram.org/bot{$supportBotToken}/setWebhook";
        $data = [
            'url' => $supportWebhookUrl,
            'allowed_updates' => ['message', 'callback_query'],
            'drop_pending_updates' => true
        ];

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);

        $result = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($result && $httpCode === 200) {
            $response = json_decode($result, true);
            return $response;
        }

        return ['ok' => false, 'description' => "HTTP Error: {$httpCode}"];
    } catch (Exception $e) {
        return ['ok' => false, 'description' => $e->getMessage()];
    }
}

/**
 * Получение информации о webhook
 */
function getSupportWebhookInfo($supportBotToken) {
    try {
        $url = "https://api.telegram.org/bot{$supportBotToken}/getWebhookInfo";
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);

        $result = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($result && $httpCode === 200) {
            $response = json_decode($result, true);
            return $response;
        }

        return ['ok' => false, 'description' => "HTTP Error: {$httpCode}"];
    } catch (Exception $e) {
        return ['ok' => false, 'description' => $e->getMessage()];
    }
}

try {
    $action = $_GET['action'] ?? 'info';
    
    // Проверяем наличие необходимых констант
    if (!defined('SUPPORT_WEBHOOK_URL') || !defined('SUPPORT_BOT_TOKEN')) {
        throw new Exception('Настройки бота поддержки не найдены. Проверьте конфигурацию.');
    }
    
    $supportWebhookUrl = SUPPORT_WEBHOOK_URL;
    $supportBotToken = SUPPORT_BOT_TOKEN;
    $supportBotUsername = defined('SUPPORT_BOT_USERNAME') ? SUPPORT_BOT_USERNAME : 'unknown';
    
    if ($action === 'info') {
        // Получаем информацию о текущем webhook
        $webhookInfo = getSupportWebhookInfo($supportBotToken);
        
        $response = [
            'success' => true,
            'current_settings' => [
                'webhook_url' => $supportWebhookUrl,
                'bot_username' => $supportBotUsername,
                'bot_token' => substr($supportBotToken, 0, 10) . '...' // Скрываем токен
            ],
            'webhook_info' => $webhookInfo,
            'needs_update' => false
        ];
        
        // Проверяем, нужно ли обновление
        if ($webhookInfo && $webhookInfo['ok'] && isset($webhookInfo['result']['url'])) {
            $currentUrl = $webhookInfo['result']['url'];
            $response['needs_update'] = ($currentUrl !== $supportWebhookUrl);
            $response['current_webhook_url'] = $currentUrl;
        }
        
    } elseif ($action === 'update') {
        // Обновляем webhook
        $updateResult = updateSupportWebhook($supportWebhookUrl, $supportBotToken);
        
        if ($updateResult && $updateResult['ok']) {
            $response = [
                'success' => true,
                'message' => 'Webhook бота поддержки успешно обновлен',
                'webhook_url' => $supportWebhookUrl,
                'bot_username' => $supportBotUsername,
                'update_result' => $updateResult
            ];
        } else {
            $error = $updateResult['description'] ?? 'Неизвестная ошибка';
            throw new Exception("Ошибка обновления webhook: {$error}");
        }
        
    } else {
        throw new Exception('Неверный параметр action. Используйте: info или update');
    }
    
} catch (Exception $e) {
    $response = [
        'success' => false,
        'error' => $e->getMessage()
    ];
}

echo json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
