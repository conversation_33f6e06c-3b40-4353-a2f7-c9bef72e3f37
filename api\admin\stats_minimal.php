<?php
/**
 * Минимальная версия stats.php для диагностики
 */

// Включаем отображение ошибок
ini_set('display_errors', 1);
ini_set('log_errors', 1);
error_reporting(E_ALL);

echo "Шаг 1: Начало<br>";

// Подключаем аутентификацию
require_once __DIR__ . '/auth.php';
echo "Шаг 2: auth.php подключен<br>";

// Проверяем аутентификацию
session_start();
if (!isAuthenticated()) {
    header('Location: login.php');
    exit;
}
echo "Шаг 3: Аутентификация пройдена<br>";

// Подключаем зависимости
require_once __DIR__ . '/../config.php';
echo "Шаг 4: config.php подключен<br>";

require_once __DIR__ . '/../db_mock.php';
echo "Шаг 5: db_mock.php подключен<br>";

// Загружаем данные
$userData = loadUserData();
echo "Шаг 6: Данные загружены (" . count($userData) . " пользователей)<br>";

// Простая статистика
$totalUsers = count($userData);
$totalBalance = 0;
$processedUsers = 0;

echo "Шаг 7: Начинаем обработку пользователей<br>";

foreach ($userData as $userId => $user) {
    $processedUsers++;
    $totalBalance += $user['balance'] ?? 0;
    
    if ($processedUsers >= 10) { // Обрабатываем только 10 пользователей
        break;
    }
}

echo "Шаг 8: Обработано $processedUsers пользователей<br>";

// Подключаем header
echo "Шаг 9: Подключаем header<br>";
include 'templates/header.php';
echo "Шаг 10: Header подключен<br>";
?>

<div class="container-fluid">
    <div class="row">
        <?php include 'templates/sidebar.php'; ?>

        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">Минимальная статистика</h1>
            </div>

            <div class="alert alert-info">
                <h4>Диагностика успешна!</h4>
                <p>Всего пользователей: <strong><?php echo $totalUsers; ?></strong></p>
                <p>Обработано: <strong><?php echo $processedUsers; ?></strong></p>
                <p>Общий баланс (первых 10): <strong><?php echo number_format($totalBalance); ?></strong></p>
            </div>

            <div class="row">
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-body">
                            <h5>Пользователей</h5>
                            <h3><?php echo $totalUsers; ?></h3>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-body">
                            <h5>Баланс (первых 10)</h5>
                            <h3><?php echo number_format($totalBalance); ?></h3>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-body">
                            <h5>Обработано</h5>
                            <h3><?php echo $processedUsers; ?></h3>
                        </div>
                    </div>
                </div>
            </div>

        </main>
    </div>
</div>

<?php include 'templates/footer.php'; ?>
