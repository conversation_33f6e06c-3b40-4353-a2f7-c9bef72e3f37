<?php
/**
 * Простой скрипт для очистки кэша статистики
 * Можно вызывать через AJAX или cron
 */

// Подключаем аутентификацию только если запрос не из командной строки
if (php_sapi_name() !== 'cli') {
    require_once __DIR__ . '/auth.php';
    session_start();
    if (!isAuthenticated()) {
        http_response_code(403);
        echo json_encode(['success' => false, 'error' => 'Unauthorized']);
        exit;
    }
    header('Content-Type: application/json');
}

function clearStatsCache() {
    $cache_dir = __DIR__ . '/../../database/';
    $cache_files = glob($cache_dir . 'stats_cache_*.json');
    $ip_cache_file = $cache_dir . 'ip_country_cache.json';
    
    $cleared = 0;
    
    // Удаляем файлы кэша статистики
    foreach ($cache_files as $file) {
        if (unlink($file)) {
            $cleared++;
        }
    }
    
    // Очищаем кэш IP стран (оставляем только последние 100 записей)
    if (file_exists($ip_cache_file)) {
        $ip_cache = json_decode(file_get_contents($ip_cache_file), true);
        if (is_array($ip_cache) && count($ip_cache) > 100) {
            $ip_cache = array_slice($ip_cache, -100, null, true);
            file_put_contents($ip_cache_file, json_encode($ip_cache, JSON_PRETTY_PRINT));
        }
    }
    
    return $cleared;
}

try {
    $cleared = clearStatsCache();
    
    $response = [
        'success' => true,
        'cleared_files' => $cleared,
        'timestamp' => date('Y-m-d H:i:s')
    ];
    
    if (php_sapi_name() === 'cli') {
        echo "Cache cleared: {$cleared} files\n";
    } else {
        echo json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    }
    
} catch (Exception $e) {
    $response = [
        'success' => false,
        'error' => $e->getMessage()
    ];
    
    if (php_sapi_name() === 'cli') {
        echo "Error: " . $e->getMessage() . "\n";
        exit(1);
    } else {
        echo json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    }
}
