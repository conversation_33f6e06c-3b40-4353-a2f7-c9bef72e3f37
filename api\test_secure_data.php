<?php
/**
 * ТЕСТИРОВАНИЕ НОВОЙ СИСТЕМЫ ЗАЩИТЫ ДАННЫХ
 * Проверяет все аспекты безопасности
 */

require_once __DIR__ . '/config.php';
require_once __DIR__ . '/secure_data_manager.php';

echo "=== ТЕСТИРОВАНИЕ СИСТЕМЫ ЗАЩИТЫ ДАННЫХ ===\n\n";

// Создаем тестовый файл
$testFile = __DIR__ . '/test_user_data.json';
$secureManager = new SecureDataManager($testFile);

// Тест 1: Сохранение валидных данных
echo "Тест 1: Сохранение валидных данных\n";
$testData = [];
for ($i = 1; $i <= 50; $i++) {
    $testData[(string)$i] = [
        'balance' => rand(0, 1000),
        'total_earned' => rand(0, 2000),
        'referrer_id' => $i > 10 ? (string)rand(1, 10) : null,
        'withdrawals' => [],
        'last_activity' => date('Y-m-d H:i:s')
    ];
}

$result = $secureManager->saveUserDataSecure($testData);
echo $result ? "✅ Успешно\n" : "❌ Ошибка\n";

// Тест 2: Загрузка данных
echo "\nТест 2: Загрузка данных\n";
$loadedData = $secureManager->loadUserData();
$isValid = is_array($loadedData) && count($loadedData) === 50;
echo $isValid ? "✅ Успешно (загружено " . count($loadedData) . " пользователей)\n" : "❌ Ошибка\n";

// Тест 3: Попытка сохранения невалидных данных (пустой массив)
echo "\nТест 3: Защита от пустых данных\n";
$result = $secureManager->saveUserDataSecure([]);
echo !$result ? "✅ Защита сработала\n" : "❌ Защита не сработала\n";

// Тест 4: Попытка сохранения данных с малым количеством пользователей
echo "\nТест 4: Защита от потери данных (мало пользователей)\n";
$smallData = ['1' => ['balance' => 100, 'referrer_id' => null]];
$result = $secureManager->saveUserDataSecure($smallData);
echo !$result ? "✅ Защита сработала\n" : "❌ Защита не сработала\n";

// Тест 5: Проверка создания бэкапов
echo "\nТест 5: Создание бэкапов\n";
$backupDir = dirname($testFile) . '/backups';
$backups = glob($backupDir . '/test_user_data_backup_*.json');
echo count($backups) > 0 ? "✅ Бэкапы создаются (" . count($backups) . " файлов)\n" : "❌ Бэкапы не создаются\n";

// Тест 6: Восстановление из бэкапа (симуляция повреждения файла)
echo "\nТест 6: Восстановление из бэкапа\n";
// Портим основной файл
file_put_contents($testFile, 'invalid json');
$restoredData = $secureManager->loadUserData();
$isRestored = is_array($restoredData) && count($restoredData) > 0;
echo $isRestored ? "✅ Восстановление работает\n" : "❌ Восстановление не работает\n";

// Тест 7: Тестирование с реальными данными (если есть)
echo "\nТест 7: Проверка реальных данных\n";
$realManager = new SecureDataManager(USER_DATA_FILE);
$realData = $realManager->loadUserData();
if ($realData !== false && count($realData) > 0) {
    echo "✅ Реальные данные загружаются (" . count($realData) . " пользователей)\n";
    
    // Проверяем структуру данных
    $validStructure = true;
    $checkedUsers = 0;
    foreach ($realData as $userId => $userData) {
        if ($checkedUsers >= 5) break; // Проверяем только первые 5
        
        if (!is_numeric($userId) || !is_array($userData) || 
            !array_key_exists('balance', $userData) || 
            !array_key_exists('referrer_id', $userData)) {
            $validStructure = false;
            break;
        }
        $checkedUsers++;
    }
    
    echo $validStructure ? "✅ Структура данных корректна\n" : "❌ Структура данных повреждена\n";
} else {
    echo "⚠️ Реальные данные не найдены или повреждены\n";
}

// Очистка тестовых файлов
echo "\nОчистка тестовых файлов...\n";
if (file_exists($testFile)) {
    unlink($testFile);
}
$testBackups = glob($backupDir . '/test_user_data_backup_*.json');
foreach ($testBackups as $backup) {
    unlink($backup);
}

echo "\n=== ТЕСТИРОВАНИЕ ЗАВЕРШЕНО ===\n";
echo "Новая система защиты данных готова к использованию!\n";
?>
