{"1986682943": {"id": 1986682943, "balance": 2, "total_earned": 2, "joined": 1752397099, "referrer_id": null, "referrals": [], "referrals_count": 0, "referral_earnings": 0, "withdrawals": [], "withdrawal_log": [], "withdrawals_count": 0, "username": "Emo82g", "first_name": "emo", "last_name": "", "language": "bg", "registered_at": 1752397099, "last_activity": 1752397099, "suspicious_activity": 0, "suspicious_activity_count": 0, "blocked": false, "device_fingerprint": "7b423c8b08e8db5bd522ccd107634b76290e2d2bed38bdddeca4603795fcb644", "fingerprint_components": {"canvas": "data:image/png;base64,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", "webgl": {"vendor": "WebKit", "renderer": "WebKit WebGL", "version": "WebGL 1.0 (OpenGL ES 2.0 Chromium)", "shadingLanguageVersion": "WebGL GLSL ES 1.0 (OpenGL ES GLSL ES 1.0 Chromium)", "unmaskedVendor": "Google Inc. (Intel)", "unmaskedRenderer": "ANGLE (Intel, Intel(R) UHD Graphics (0x000046D1) Direct3D11 vs_5_0 ps_5_0, D3D11)", "extensions": "ANGLE_instanced_arrays,EXT_blend_minmax,EXT_clip_control,EXT_color_buffer_half_float,EXT_depth_clamp,EXT_disjoint_timer_query,EXT_float_blend,EXT_frag_depth,EXT_polygon_offset_clamp,EXT_shader_texture_lod,EXT_texture_compression_bptc,EXT_texture_compression_rgtc,EXT_texture_filter_anisotropic,EXT_texture_mirror_clamp_to_edge,EXT_sRGB,KHR_parallel_shader_compile,OES_element_index_uint,OES_fbo_render_mipmap,OES_standard_derivatives,OES_texture_float,OES_texture_float_linear,OES_texture_half_float,OES_texture_half_float_linear,OES_vertex_array_object,WEBGL_blend_func_extended,WEBGL_color_buffer_float,WEBGL_compressed_texture_s3tc,WEBGL_compressed_texture_s3tc_srgb,WEBGL_debug_renderer_info,WEBGL_debug_shaders,WEBGL_depth_texture,WEBGL_draw_buffers,WEBGL_lose_context,WEBGL_multi_draw,WEBGL_polygon_mode", "maxTextureSize": 16384, "maxViewportDims": "32767,32767"}, "audio": "0,0.08264365047216415,0.16771934926509857,0.2492087185382843,0.3338141441345215,0.4166487157344818,0.5001381635665894,0.5834451913833618,0.6663544178009033,0.7517519593238831,0.8300729393959045,0.9228833317756653,0.9787084460258484,0.9228832721710205,0.8300728797912598,0.7517519593238831,0.6663542985916138,0.583445131778717,0.5001381635665894,0.41664862632751465,0.3338140547275543,0.2492087185382843,0.1677192896604538,0.08264358341693878,0,-0.08264380693435669,-0.16771942377090454,-0.2492087185382843,-0.3338142931461334,-0.4166487753391266,-0.5001381635665894,-0.5834453105926514,-0.6663544774055481,-0.7517519593238831,-0.8300730586051941,-0.9228834509849548,-0.9787084460258484,-0.9228830933570862,-0.8300728797912598,-0.7517519593238831,-0.6663541793823242,-0.583445131778717,-0.5001381635665894,-0.4166485071182251,-0.3338140547275543,-0.2492087185382843,-0.16771917045116425,-0.08264358341693878,1.1521713361162256e-7,0.0826437696814537,0.16771946847438812,0.24920883774757385,0.33381426334381104,0.41664883494377136,0.5001382827758789,0.5834453105926514,0.6663545370101929,0.7517520785331726,0.8300730586051941,0.9228835105895996,0.9787084460258484,0.9228830933570862,0.8300727605819702,0.7517518401145935,0.6663541793823242,0.5834450125694275,0.5001380443572998,0.4166485071182251,0.33381393551826477,0.24920859932899475,0.16771917045116425,0.08264345675706863,-2.3043426722324512e-7,-0.08264380693435669,-0.16771942377090454,-0.2492089569568634,-0.3338142931461334,-0.4166487753391266,-0.5001384019851685,-0.5834453105926514,-0.6663547158241272,-0.7517521977424622,-0.8300730586051941,-0.9228836894035339,-0.9787084460258484,-0.9228830933570862,-0.8300726413726807,-0.751751720905304,-0.6663541793823242,-0.5834448933601379,-0.5001379251480103,-0.4166485071182251,-0.3338138163089752,-0.2492084950208664,-0.16771917045116425,-0.08264333754777908,2.3043426722324512e-7,0.08264389634132385,0.16771958768367767,0.2492089569568634", "screen": {"width": 1164, "height": 655, "availWidth": 1164, "availHeight": 611, "colorDepth": 24, "pixelDepth": 24, "orientation": "landscape-primary", "devicePixelRatio": 1.6500000953674316, "innerWidth": 477, "innerHeight": 482, "outerWidth": 477, "outerHeight": 482}, "system": {"platform": "Win32", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "language": "bg", "languages": "bg,en,en-GB,en-US", "hardwareConcurrency": 4, "deviceMemory": 8, "maxTouchPoints": 0, "cookieEnabled": true, "doNotTrack": "unknown", "onLine": true}, "navigator": {"plugins": "PDF Viewer,Chrome PDF Viewer,Chromium PDF Viewer,Microsoft Edge PDF Viewer,WebKit built-in PDF", "mimeTypes": "application/pdf,text/pdf", "vendor": "Google Inc.", "product": "Gecko", "productSub": "20030107", "appName": "Netscape", "appVersion": "5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}, "timezone": {"timezone": "Europe/Sofia", "timezoneOffset": -180, "locale": "bg-BG", "calendar": "gregory", "numberingSystem": "latn"}, "fonts": "Arial,Helvetica,Times New Roman,Courier New,Verdana,Georgia,Comic Sans MS,Trebuchet MS,Arial Black,Impact,Tahoma,Calibri"}, "fingerprint_registered_at": 1752397254, "fingerprint_ip": "*************", "ad_views_log": [{"time": 1752397258, "reward": 2, "type": "rewarded_video"}]}, "7167106424": {"id": 7167106424, "balance": 0, "total_earned": 0, "joined": 1752400121, "referrer_id": null, "referrals": [7628965068, 375105832], "referrals_count": 2, "referral_earnings": 0, "withdrawals": [], "withdrawal_log": [], "withdrawals_count": 0, "username": "kHAILiDs", "first_name": "KHALID", "last_name": "NodeGo.Ai SparkChain.AI", "language": "en", "registered_at": 1752400121, "last_activity": 1752400479, "suspicious_activity": 0, "suspicious_activity_count": 0, "blocked": false, "device_fingerprint": "068fa729c21865be1f5870f19461acfb065988e1610f9c5a6a4df774a7cfd7e6", "fingerprint_components": {"canvas": "data:image/png;base64,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", "webgl": {"vendor": "WebKit", "renderer": "WebKit WebGL", "version": "WebGL 1.0 (OpenGL ES 2.0 Chromium)", "shadingLanguageVersion": "WebGL GLSL ES 1.0 (OpenGL ES GLSL ES 1.0 Chromium)", "unmaskedVendor": "Imagination Technologies", "unmaskedRenderer": "PowerVR SGX Doma", "extensions": "ANGLE_instanced_arrays,EXT_blend_minmax,EXT_color_buffer_half_float,EXT_float_blend,EXT_shader_texture_lod,OES_element_index_uint,OES_fbo_render_mipmap,OES_standard_derivatives,OES_texture_float,OES_texture_half_float,OES_vertex_array_object,WEBGL_color_buffer_float,WEBGL_compressed_texture_astc,WEBGL_compressed_texture_etc,WEBGL_compressed_texture_etc1,WEBGL_compressed_texture_pvrtc,WEBGL_debug_renderer_info,WEBGL_debug_shaders,WEBGL_depth_texture,WEBGL_draw_buffers,WEBGL_lose_context,WEBGL_multi_draw", "maxTextureSize": 4096, "maxViewportDims": "4096,4096"}, "audio": "0,0.08264365047216415,0.16771934926509857,0.2492087185382843,0.3338141441345215,0.4166487157344818,0.5001381635665894,0.5834451913833618,0.6663544178009033,0.7517519593238831,0.8300729393959045,0.9228833317756653,0.9787084460258484,0.9228832721710205,0.8300728797912598,0.7517519593238831,0.6663542985916138,0.583445131778717,0.5001381635665894,0.41664862632751465,0.3338140547275543,0.2492087185382843,0.1677192896604538,0.08264358341693878,0,-0.08264380693435669,-0.16771942377090454,-0.2492087185382843,-0.3338142931461334,-0.4166487753391266,-0.5001381635665894,-0.5834453105926514,-0.6663544774055481,-0.7517519593238831,-0.8300730586051941,-0.9228834509849548,-0.9787084460258484,-0.9228830933570862,-0.8300728797912598,-0.7517519593238831,-0.6663541793823242,-0.583445131778717,-0.5001381635665894,-0.4166485071182251,-0.3338140547275543,-0.2492087185382843,-0.16771917045116425,-0.08264358341693878,1.1521713361162256e-7,0.0826437696814537,0.16771946847438812,0.24920883774757385,0.33381426334381104,0.41664883494377136,0.5001382827758789,0.5834453105926514,0.6663545370101929,0.7517520785331726,0.8300730586051941,0.9228835105895996,0.9787084460258484,0.9228830933570862,0.8300727605819702,0.7517518401145935,0.6663541793823242,0.5834450125694275,0.5001380443572998,0.4166485071182251,0.33381393551826477,0.24920859932899475,0.16771917045116425,0.08264345675706863,-2.3043426722324512e-7,-0.08264380693435669,-0.16771942377090454,-0.2492089569568634,-0.3338142931461334,-0.4166487753391266,-0.5001384019851685,-0.5834453105926514,-0.6663547158241272,-0.7517521977424622,-0.8300730586051941,-0.9228836894035339,-0.9787084460258484,-0.9228830933570862,-0.8300726413726807,-0.751751720905304,-0.6663541793823242,-0.5834448933601379,-0.5001379251480103,-0.4166485071182251,-0.3338138163089752,-0.2492084950208664,-0.16771917045116425,-0.08264333754777908,2.3043426722324512e-7,0.08264389634132385,0.16771958768367767,0.2492089569568634", "screen": {"width": 412, "height": 915, "availWidth": 412, "availHeight": 915, "colorDepth": 24, "pixelDepth": 24, "orientation": "portrait-primary", "devicePixelRatio": 1.75, "innerWidth": 411, "innerHeight": 814, "outerWidth": 412, "outerHeight": 814}, "system": {"platform": "Linux armv7l", "userAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.7151.115 Mobile Safari/537.36 Telegram-Android/11.12.0 (Wiko W-V730-E<PERSON>; Android 10; SDK 29; LOW)", "language": "en", "languages": "en,en-US", "hardwareConcurrency": 8, "deviceMemory": 2, "maxTouchPoints": 5, "cookieEnabled": true, "doNotTrack": "unknown", "onLine": true}, "navigator": {"plugins": "", "mimeTypes": "", "vendor": "Google Inc.", "product": "Gecko", "productSub": "20030107", "appName": "Netscape", "appVersion": "5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.7151.115 Mobile Safari/537.36 Telegram-Android/11.12.0 (Wiko W-V730-EEA; Android 10; SDK 29; LOW)"}, "timezone": {"timezone": "Africa/Addis_Ababa", "timezoneOffset": -180, "locale": "en", "calendar": "gregory", "numberingSystem": "latn"}, "fonts": "Arial,Helvetica,Times New Roman,Courier New,Verdana,Georgia,Palatino,Tahoma"}, "fingerprint_registered_at": 1752400524, "fingerprint_ip": "**************", "fraud_detected": true, "fraud_reason": "suspicious_screen_resolution, no_plugins", "fraud_detected_at": 1752400524}, "7628965068": {"balance": 0, "total_earned": 0, "withdrawals": [], "withdrawal_log": [], "referrer_id": 7167106424, "referrals": [], "referral_earnings": 0, "first_name": "Hindiya", "last_name": "", "username": "Ofisod", "language": "en", "registered_at": 1752400594, "last_activity": 1752400594, "suspicious_activity_count": 0, "withdrawals_count": 0}, "521787045": {"id": 521787045, "balance": 0, "total_earned": 0, "joined": 1752400796, "referrer_id": null, "referrals": [], "referrals_count": 0, "referral_earnings": 0, "withdrawals": [], "withdrawal_log": [], "withdrawals_count": 0, "username": "Jomakavelionly", "first_name": "<PERSON><PERSON><PERSON>", "last_name": "<PERSON><PERSON><PERSON>", "language": "en", "registered_at": 1752400796, "last_activity": 1752400796, "suspicious_activity": 0, "suspicious_activity_count": 0, "blocked": false, "device_fingerprint": "5d0e09488156937453718e01fa83bec74bc51362d8857101b9f3e570bf246003", "fingerprint_components": {"canvas": "data:image/png;base64,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", "webgl": {"vendor": "WebKit", "renderer": "WebKit WebGL", "version": "WebGL 1.0 (OpenGL ES 2.0 Chromium)", "shadingLanguageVersion": "WebGL GLSL ES 1.0 (OpenGL ES GLSL ES 1.0 Chromium)", "unmaskedVendor": "ARM", "unmaskedRenderer": "Mali-G57 MC2", "extensions": "ANGLE_instanced_arrays,EXT_blend_minmax,EXT_float_blend,EXT_texture_filter_anisotropic,EXT_sRGB,OES_element_index_uint,OES_fbo_render_mipmap,OES_standard_derivatives,OES_texture_float_linear,OES_vertex_array_object,WEBGL_compressed_texture_astc,WEBGL_compressed_texture_etc,WEBGL_compressed_texture_etc1,WEBGL_debug_renderer_info,WEBGL_debug_shaders,WEBGL_depth_texture,WEBGL_lose_context,WEBGL_multi_draw", "maxTextureSize": 4096, "maxViewportDims": "16383,16383"}, "audio": "0,0.08264365047216415,0.16771934926509857,0.2492087185382843,0.3338141441345215,0.4166487157344818,0.5001381635665894,0.5834451913833618,0.6663544178009033,0.7517519593238831,0.8300729393959045,0.9228833317756653,0.9787084460258484,0.9228832721710205,0.8300728797912598,0.7517519593238831,0.6663542985916138,0.583445131778717,0.5001381635665894,0.41664862632751465,0.3338140547275543,0.2492087185382843,0.1677192896604538,0.08264358341693878,0,-0.08264380693435669,-0.16771942377090454,-0.2492087185382843,-0.3338142931461334,-0.4166487753391266,-0.5001381635665894,-0.5834453105926514,-0.6663544774055481,-0.7517519593238831,-0.8300730586051941,-0.9228834509849548,-0.9787084460258484,-0.9228830933570862,-0.8300728797912598,-0.7517519593238831,-0.6663541793823242,-0.583445131778717,-0.5001381635665894,-0.4166485071182251,-0.3338140547275543,-0.2492087185382843,-0.16771917045116425,-0.08264358341693878,1.1521713361162256e-7,0.0826437696814537,0.16771946847438812,0.24920883774757385,0.33381426334381104,0.41664883494377136,0.5001382827758789,0.5834453105926514,0.6663545370101929,0.7517520785331726,0.8300730586051941,0.9228835105895996,0.9787084460258484,0.9228830933570862,0.8300727605819702,0.7517518401145935,0.6663541793823242,0.5834450125694275,0.5001380443572998,0.4166485071182251,0.33381393551826477,0.24920859932899475,0.16771917045116425,0.08264345675706863,-2.3043426722324512e-7,-0.08264380693435669,-0.16771942377090454,-0.2492089569568634,-0.3338142931461334,-0.4166487753391266,-0.5001384019851685,-0.5834453105926514,-0.6663547158241272,-0.7517521977424622,-0.8300730586051941,-0.9228836894035339,-0.9787084460258484,-0.9228830933570862,-0.8300726413726807,-0.751751720905304,-0.6663541793823242,-0.5834448933601379,-0.5001379251480103,-0.4166485071182251,-0.3338138163089752,-0.2492084950208664,-0.16771917045116425,-0.08264333754777908,2.3043426722324512e-7,0.08264389634132385,0.16771958768367767,0.2492089569568634", "screen": {"width": 393, "height": 873, "availWidth": 393, "availHeight": 873, "colorDepth": 24, "pixelDepth": 24, "orientation": "portrait-primary", "devicePixelRatio": 2.75, "innerWidth": 392, "innerHeight": 767, "outerWidth": 393, "outerHeight": 767}, "system": {"platform": "Linux aarch64", "userAgent": "Mozilla/5.0 (Linux; Android 13; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.45 Mobile Safari/537.36 Telegram-Android/11.13.2 (Xiaomi 2201116TG; Android 13; SDK 33; AVERAGE)", "language": "en", "languages": "en,en-GB,en-US", "hardwareConcurrency": 8, "deviceMemory": 8, "maxTouchPoints": 5, "cookieEnabled": true, "doNotTrack": "unknown", "onLine": true}, "navigator": {"plugins": "", "mimeTypes": "", "vendor": "Google Inc.", "product": "Gecko", "productSub": "20030107", "appName": "Netscape", "appVersion": "5.0 (Linux; Android 13; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.45 Mobile Safari/537.36 Telegram-Android/11.13.2 (Xiaomi 2201116TG; Android 13; SDK 33; AVERA<PERSON>)"}, "timezone": {"timezone": "Africa/Addis_Ababa", "timezoneOffset": -180, "locale": "en", "calendar": "gregory", "numberingSystem": "latn"}, "fonts": "Arial,Helvetica,Times New Roman,Courier New,Verdana,Georgia,Palatino,Tahoma"}, "fingerprint_registered_at": 1752400796, "fingerprint_ip": "*************", "fraud_detected": true, "fraud_reason": "suspicious_screen_resolution, no_plugins", "fraud_detected_at": 1752400796}, "648309472": {"balance": 0, "total_earned": 0, "withdrawals": [], "withdrawal_log": [], "referrer_id": null, "referrals": [], "referral_earnings": 0, "first_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "last_name": "", "username": "ermatov_sh55", "language": "ru", "registered_at": 1752400828, "last_activity": 1752400828, "suspicious_activity_count": 0, "withdrawals_count": 0, "device_fingerprint": "06f52c47aed849c5faab74115665cae437f9ae240520f527ab842e0bcffaabd0", "fingerprint_components": {"canvas": "data:image/png;base64,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", "webgl": {"vendor": "WebKit", "renderer": "WebKit WebGL", "version": "WebGL 1.0 (OpenGL ES 2.0 Chromium)", "shadingLanguageVersion": "WebGL GLSL ES 1.0 (OpenGL ES GLSL ES 1.0 Chromium)", "unmaskedVendor": "Google Inc. (Intel)", "unmaskedRenderer": "ANGLE (Intel, Intel(R) HD Graphics Direct3D9Ex vs_3_0 ps_3_0, igdumd64.dll)", "extensions": "ANGLE_instanced_arrays,EXT_blend_minmax,EXT_color_buffer_half_float,EXT_float_blend,EXT_frag_depth,EXT_shader_texture_lod,EXT_texture_filter_anisotropic,EXT_texture_mirror_clamp_to_edge,EXT_sRGB,KHR_parallel_shader_compile,OES_element_index_uint,OES_fbo_render_mipmap,OES_standard_derivatives,OES_texture_float,OES_texture_float_linear,OES_texture_half_float,OES_texture_half_float_linear,OES_vertex_array_object,WEBGL_color_buffer_float,WEBGL_compressed_texture_s3tc,WEBGL_compressed_texture_s3tc_srgb,WEBGL_debug_renderer_info,WEBGL_debug_shaders,WEBGL_depth_texture,WEBGL_lose_context,WEBGL_multi_draw", "maxTextureSize": 8192, "maxViewportDims": "8192,8192"}, "audio": "0,0.08264365047216415,0.16771934926509857,0.2492087185382843,0.3338141441345215,0.4166487157344818,0.5001381635665894,0.5834451913833618,0.6663544178009033,0.7517519593238831,0.8300729393959045,0.9228833317756653,0.9787084460258484,0.9228832721710205,0.8300728797912598,0.7517519593238831,0.6663542985916138,0.583445131778717,0.5001381635665894,0.41664862632751465,0.3338140547275543,0.2492087185382843,0.1677192896604538,0.08264358341693878,0,-0.08264380693435669,-0.16771942377090454,-0.2492087185382843,-0.3338142931461334,-0.4166487753391266,-0.5001381635665894,-0.5834453105926514,-0.6663544774055481,-0.7517519593238831,-0.8300730586051941,-0.9228834509849548,-0.9787084460258484,-0.9228830933570862,-0.8300728797912598,-0.7517519593238831,-0.6663541793823242,-0.583445131778717,-0.5001381635665894,-0.4166485071182251,-0.3338140547275543,-0.2492087185382843,-0.16771917045116425,-0.08264358341693878,1.1521713361162256e-7,0.0826437696814537,0.16771946847438812,0.24920883774757385,0.33381426334381104,0.41664883494377136,0.5001382827758789,0.5834453105926514,0.6663545370101929,0.7517520785331726,0.8300730586051941,0.9228835105895996,0.9787084460258484,0.9228830933570862,0.8300727605819702,0.7517518401145935,0.6663541793823242,0.5834450125694275,0.5001380443572998,0.4166485071182251,0.33381393551826477,0.24920859932899475,0.16771917045116425,0.08264345675706863,-2.3043426722324512e-7,-0.08264380693435669,-0.16771942377090454,-0.2492089569568634,-0.3338142931461334,-0.4166487753391266,-0.5001384019851685,-0.5834453105926514,-0.6663547158241272,-0.7517521977424622,-0.8300730586051941,-0.9228836894035339,-0.9787084460258484,-0.9228830933570862,-0.8300726413726807,-0.751751720905304,-0.6663541793823242,-0.5834448933601379,-0.5001379251480103,-0.4166485071182251,-0.3338138163089752,-0.2492084950208664,-0.16771917045116425,-0.08264333754777908,2.3043426722324512e-7,0.08264389634132385,0.16771958768367767,0.2492089569568634", "screen": {"width": 1366, "height": 768, "availWidth": 1366, "availHeight": 728, "colorDepth": 24, "pixelDepth": 24, "orientation": "landscape-primary", "devicePixelRatio": 1, "innerWidth": 384, "innerHeight": 590, "outerWidth": 384, "outerHeight": 590}, "system": {"platform": "Win32", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "language": "ru", "languages": "ru,en,en-GB,en-US", "hardwareConcurrency": 4, "deviceMemory": 4, "maxTouchPoints": 0, "cookieEnabled": true, "doNotTrack": "unknown", "onLine": true}, "navigator": {"plugins": "PDF Viewer,Chrome PDF Viewer,Chromium PDF Viewer,Microsoft Edge PDF Viewer,WebKit built-in PDF", "mimeTypes": "application/pdf,text/pdf", "vendor": "Google Inc.", "product": "Gecko", "productSub": "20030107", "appName": "Netscape", "appVersion": "5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}, "timezone": {"timezone": "Asia/Tashkent", "timezoneOffset": -300, "locale": "ru-RU", "calendar": "gregory", "numberingSystem": "latn"}, "fonts": "Arial,Helvetica,Times New Roman,Courier New,Verdana,Georgia,Garamond,Comic Sans MS,Trebuchet MS,Arial Black,Impact,Tahoma,Calibri"}, "fingerprint_registered_at": 1752400848, "fingerprint_ip": "**************", "id": 648309472, "referrals_count": 0, "joined": 1752400854, "suspicious_activity": 0, "blocked": false}, "6754811658": {"id": 6754811658, "balance": 0, "total_earned": 0, "joined": 1752400864, "referrer_id": null, "referrals": [], "referrals_count": 0, "referral_earnings": 0, "withdrawals": [], "withdrawal_log": [], "withdrawals_count": 0, "username": "kalayu1234", "first_name": "<PERSON><PERSON><PERSON>", "last_name": "", "language": "en", "registered_at": 1752400864, "last_activity": 1752400864, "suspicious_activity": 0, "suspicious_activity_count": 0, "blocked": false, "device_fingerprint": "e370d5dd91c49ee3c56d4503ee96000149801d23dd0ad0c07ade90440ffd05d4", "fingerprint_components": {"canvas": "data:image/png;base64,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", "webgl": {"vendor": "WebKit", "renderer": "WebKit WebGL", "version": "WebGL 1.0 (OpenGL ES 2.0 Chromium)", "shadingLanguageVersion": "WebGL GLSL ES 1.0 (OpenGL ES GLSL ES 1.0 Chromium)", "unmaskedVendor": "ARM", "unmaskedRenderer": "Mali-G57 MC2", "extensions": "ANGLE_instanced_arrays,EXT_blend_minmax,EXT_float_blend,EXT_texture_filter_anisotropic,EXT_sRGB,OES_element_index_uint,OES_fbo_render_mipmap,OES_standard_derivatives,OES_texture_float_linear,OES_vertex_array_object,WEBGL_compressed_texture_astc,WEBGL_compressed_texture_etc,WEBGL_compressed_texture_etc1,WEBGL_debug_renderer_info,WEBGL_debug_shaders,WEBGL_depth_texture,WEBGL_lose_context,WEBGL_multi_draw", "maxTextureSize": 4096, "maxViewportDims": "16383,16383"}, "audio": "audio_timeout", "screen": {"width": 393, "height": 873, "availWidth": 393, "availHeight": 873, "colorDepth": 24, "pixelDepth": 24, "orientation": "portrait-primary", "devicePixelRatio": 2.75, "innerWidth": 392, "innerHeight": 767, "outerWidth": 393, "outerHeight": 767}, "system": {"platform": "Linux aarch64", "userAgent": "Mozilla/5.0 (Linux; Android 13; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.45 Mobile Safari/537.36 Telegram-Android/11.13.2 (Xiaomi 2201116TG; Android 13; SDK 33; AVERAGE)", "language": "en", "languages": "en,en-GB,en-US", "hardwareConcurrency": 8, "deviceMemory": 8, "maxTouchPoints": 5, "cookieEnabled": true, "doNotTrack": "unknown", "onLine": true}, "navigator": {"plugins": "", "mimeTypes": "", "vendor": "Google Inc.", "product": "Gecko", "productSub": "20030107", "appName": "Netscape", "appVersion": "5.0 (Linux; Android 13; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.45 Mobile Safari/537.36 Telegram-Android/11.13.2 (Xiaomi 2201116TG; Android 13; SDK 33; AVERA<PERSON>)"}, "timezone": {"timezone": "Africa/Addis_Ababa", "timezoneOffset": -180, "locale": "en", "calendar": "gregory", "numberingSystem": "latn"}, "fonts": "Arial,Helvetica,Times New Roman,Courier New,Verdana,Georgia,Palatino,Tahoma"}, "fingerprint_registered_at": 1752400864, "fingerprint_ip": "*************", "fraud_detected": true, "fraud_reason": "suspicious_screen_resolution, no_plugins", "fraud_detected_at": 1752400864}, "5496219469": {"id": 5496219469, "balance": 0, "total_earned": 0, "joined": 1752402158, "referrer_id": null, "referrals": [], "referrals_count": 0, "referral_earnings": 0, "withdrawals": [], "withdrawal_log": [], "withdrawals_count": 0, "username": "jaybird59", "first_name": "<PERSON>", "last_name": "", "language": "en", "registered_at": 1752402158, "last_activity": 1752402158, "suspicious_activity": 0, "suspicious_activity_count": 0, "blocked": false, "device_fingerprint": "95bda62a1a716290e16de135e71396b4f6ae515fd18f34efcb547172c4deeabb", "fingerprint_components": {"canvas": "data:image/png;base64,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", "webgl": {"vendor": "WebKit", "renderer": "WebKit WebGL", "version": "WebGL 1.0 (OpenGL ES 2.0 Chromium)", "shadingLanguageVersion": "WebGL GLSL ES 1.0 (OpenGL ES GLSL ES 1.0 Chromium)", "unmaskedVendor": "ARM", "unmaskedRenderer": "Mali-G57", "extensions": "ANGLE_instanced_arrays,EXT_blend_minmax,EXT_float_blend,EXT_texture_filter_anisotropic,EXT_sRGB,OES_element_index_uint,OES_fbo_render_mipmap,OES_standard_derivatives,OES_texture_float_linear,OES_vertex_array_object,WEBGL_compressed_texture_astc,WEBGL_compressed_texture_etc,WEBGL_compressed_texture_etc1,WEBGL_debug_renderer_info,WEBGL_debug_shaders,WEBGL_depth_texture,WEBGL_lose_context,WEBGL_multi_draw", "maxTextureSize": 4096, "maxViewportDims": "16383,16383"}, "audio": "0,0.08264365047216415,0.16771934926509857,0.2492087185382843,0.3338141441345215,0.4166487157344818,0.5001381635665894,0.5834451913833618,0.6663544178009033,0.7517519593238831,0.8300729393959045,0.9228833317756653,0.9787084460258484,0.9228832721710205,0.8300728797912598,0.7517519593238831,0.6663542985916138,0.583445131778717,0.5001381635665894,0.41664862632751465,0.3338140547275543,0.2492087185382843,0.1677192896604538,0.08264358341693878,0,-0.08264380693435669,-0.16771942377090454,-0.2492087185382843,-0.3338142931461334,-0.4166487753391266,-0.5001381635665894,-0.5834453105926514,-0.6663544774055481,-0.7517519593238831,-0.8300730586051941,-0.9228834509849548,-0.9787084460258484,-0.9228830933570862,-0.8300728797912598,-0.7517519593238831,-0.6663541793823242,-0.583445131778717,-0.5001381635665894,-0.4166485071182251,-0.3338140547275543,-0.2492087185382843,-0.16771917045116425,-0.08264358341693878,1.1521713361162256e-7,0.0826437696814537,0.16771946847438812,0.24920883774757385,0.33381426334381104,0.41664883494377136,0.5001382827758789,0.5834453105926514,0.6663545370101929,0.7517520785331726,0.8300730586051941,0.9228835105895996,0.9787084460258484,0.9228830933570862,0.8300727605819702,0.7517518401145935,0.6663541793823242,0.5834450125694275,0.5001380443572998,0.4166485071182251,0.33381393551826477,0.24920859932899475,0.16771917045116425,0.08264345675706863,-2.3043426722324512e-7,-0.08264380693435669,-0.16771942377090454,-0.2492089569568634,-0.3338142931461334,-0.4166487753391266,-0.5001384019851685,-0.5834453105926514,-0.6663547158241272,-0.7517521977424622,-0.8300730586051941,-0.9228836894035339,-0.9787084460258484,-0.9228830933570862,-0.8300726413726807,-0.751751720905304,-0.6663541793823242,-0.5834448933601379,-0.5001379251480103,-0.4166485071182251,-0.3338138163089752,-0.2492084950208664,-0.16771917045116425,-0.08264333754777908,2.3043426722324512e-7,0.08264389634132385,0.16771958768367767,0.2492089569568634", "screen": {"width": 360, "height": 806, "availWidth": 360, "availHeight": 806, "colorDepth": 24, "pixelDepth": 24, "orientation": "portrait-primary", "devicePixelRatio": 2, "innerWidth": 360, "innerHeight": 662, "outerWidth": 360, "outerHeight": 662}, "system": {"platform": "Linux armv8l", "userAgent": "Mozilla/5.0 (Linux; Android 13; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.7151.118 Mobile Safari/537.36 Telegram-Android/11.13.2 (Infinix Infinix X6525; Android 13; SDK 33; AVERAGE)", "language": "en", "languages": "en,en-US", "hardwareConcurrency": 8, "deviceMemory": 2, "maxTouchPoints": 2, "cookieEnabled": true, "doNotTrack": "unknown", "onLine": true}, "navigator": {"plugins": "", "mimeTypes": "", "vendor": "Google Inc.", "product": "Gecko", "productSub": "20030107", "appName": "Netscape", "appVersion": "5.0 (Linux; Android 13; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.7151.118 Mobile Safari/537.36 Telegram-Android/11.13.2 (Infinix Infinix X6525; Android 13; SDK 33; AVERAGE)"}, "timezone": {"timezone": "Africa/Lagos", "timezoneOffset": -60, "locale": "en", "calendar": "gregory", "numberingSystem": "latn"}, "fonts": "Arial,Helvetica,Times New Roman,Courier New,Verdana,Georgia,Palatino,Tahoma"}, "fingerprint_registered_at": 1752402212, "fingerprint_ip": "**************", "fraud_detected": true, "fraud_reason": "suspicious_screen_resolution, no_plugins", "fraud_detected_at": 1752402212}, "7666797751": {"balance": 0, "total_earned": 0, "withdrawals": [], "withdrawal_log": [], "referrer_id": null, "referrals": [], "referral_earnings": 0, "first_name": "Lola", "last_name": "", "username": "Fojdlpc", "language": "en", "registered_at": 1752403022, "last_activity": 1752403022, "suspicious_activity_count": 0, "withdrawals_count": 0, "device_fingerprint": "51637257a1c9bc16df35ca51a67010d57c0ac3835bb84b5ce617f5f84b485b0f", "fingerprint_components": {"canvas": "data:image/png;base64,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", "webgl": {"vendor": "WebKit", "renderer": "WebKit WebGL", "version": "WebGL 1.0 (OpenGL ES 2.0 Chromium)", "shadingLanguageVersion": "WebGL GLSL ES 1.0 (OpenGL ES GLSL ES 1.0 Chromium)", "unmaskedVendor": "Imagination Technologies", "unmaskedRenderer": "PowerVR SGX Doma", "extensions": "ANGLE_instanced_arrays,EXT_blend_minmax,EXT_color_buffer_half_float,EXT_float_blend,EXT_shader_texture_lod,OES_element_index_uint,OES_fbo_render_mipmap,OES_standard_derivatives,OES_texture_float,OES_texture_half_float,OES_vertex_array_object,WEBGL_color_buffer_float,WEBGL_compressed_texture_astc,WEBGL_compressed_texture_etc,WEBGL_compressed_texture_etc1,WEBGL_compressed_texture_pvrtc,WEBGL_debug_renderer_info,WEBGL_debug_shaders,WEBGL_depth_texture,WEBGL_draw_buffers,WEBGL_lose_context,WEBGL_multi_draw", "maxTextureSize": 4096, "maxViewportDims": "4096,4096"}, "audio": "audio_timeout", "screen": {"width": 412, "height": 915, "availWidth": 412, "availHeight": 915, "colorDepth": 24, "pixelDepth": 24, "orientation": "portrait-primary", "devicePixelRatio": 1.75, "innerWidth": 411, "innerHeight": 814, "outerWidth": 412, "outerHeight": 814}, "system": {"platform": "Linux armv7l", "userAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.7151.115 Mobile Safari/537.36 Telegram-Android/T11.9.1 - P11.20.0 (Wiko W-V730-EEA; Android 10; SDK 29; LOW)", "language": "en-US", "languages": "en-US", "hardwareConcurrency": 8, "deviceMemory": 2, "maxTouchPoints": 5, "cookieEnabled": true, "doNotTrack": "unknown", "onLine": false}, "navigator": {"plugins": "", "mimeTypes": "", "vendor": "Google Inc.", "product": "Gecko", "productSub": "20030107", "appName": "Netscape", "appVersion": "5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.7151.115 Mobile Safari/537.36 Telegram-Android/T11.9.1 - P11.20.0 (Wiko W-V730-EEA; Android 10; SDK 29; LOW)"}, "timezone": {"timezone": "Africa/Addis_Ababa", "timezoneOffset": -180, "locale": "en", "calendar": "gregory", "numberingSystem": "latn"}, "fonts": "Arial,Helvetica,Times New Roman,Courier New,Verdana,Georgia,Palatino,Tahoma"}, "fingerprint_registered_at": 1752403313, "fingerprint_ip": "**************", "fraud_detected": true, "fraud_reason": "suspicious_screen_resolution, no_plugins", "fraud_detected_at": 1752403313, "id": 7666797751, "referrals_count": 0, "joined": 1752403043, "suspicious_activity": 0, "blocked": false}, "375105832": {"balance": 0, "total_earned": 0, "withdrawals": [], "withdrawal_log": [], "referrer_id": 7167106424, "referrals": [], "referral_earnings": 0, "first_name": "👑", "last_name": "", "username": "<PERSON><PERSON><PERSON><PERSON>", "language": "en", "registered_at": 1752404901, "last_activity": 1752404901, "suspicious_activity_count": 0, "withdrawals_count": 0, "device_fingerprint": "ee36ad71db3a008785f86835f3a7b9376fe002b3fb753bb29aff2644cf702465", "fingerprint_components": {"canvas": "data:image/png;base64,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", "webgl": {"vendor": "WebKit", "renderer": "WebKit WebGL", "version": "WebGL 1.0 (OpenGL ES 2.0 Chromium)", "shadingLanguageVersion": "WebGL GLSL ES 1.0 (OpenGL ES GLSL ES 1.0 Chromium)", "unmaskedVendor": "ARM", "unmaskedRenderer": "Mali-G71", "extensions": "ANGLE_instanced_arrays,EXT_blend_minmax,EXT_float_blend,EXT_sRGB,OES_element_index_uint,OES_fbo_render_mipmap,OES_standard_derivatives,OES_vertex_array_object,WEBGL_compressed_texture_astc,WEBGL_compressed_texture_etc,WEBGL_compressed_texture_etc1,WEBGL_debug_renderer_info,WEBGL_debug_shaders,WEBGL_depth_texture,WEBGL_lose_context,WEBGL_multi_draw", "maxTextureSize": 4096, "maxViewportDims": "8192,8192"}, "audio": "0,0.08264365047216415,0.16771934926509857,0.2492087185382843,0.3338141441345215,0.4166487157344818,0.5001381635665894,0.5834451913833618,0.6663544178009033,0.7517519593238831,0.8300729393959045,0.9228833317756653,0.9787084460258484,0.9228832721710205,0.8300728797912598,0.7517519593238831,0.6663542985916138,0.583445131778717,0.5001381635665894,0.41664862632751465,0.3338140547275543,0.2492087185382843,0.1677192896604538,0.08264358341693878,0,-0.08264380693435669,-0.16771942377090454,-0.2492087185382843,-0.3338142931461334,-0.4166487753391266,-0.5001381635665894,-0.5834453105926514,-0.6663544774055481,-0.7517519593238831,-0.8300730586051941,-0.9228834509849548,-0.9787084460258484,-0.9228830933570862,-0.8300728797912598,-0.7517519593238831,-0.6663541793823242,-0.583445131778717,-0.5001381635665894,-0.4166485071182251,-0.3338140547275543,-0.2492087185382843,-0.16771917045116425,-0.08264358341693878,1.1521713361162256e-7,0.0826437696814537,0.16771946847438812,0.24920883774757385,0.33381426334381104,0.41664883494377136,0.5001382827758789,0.5834453105926514,0.6663545370101929,0.7517520785331726,0.8300730586051941,0.9228835105895996,0.9787084460258484,0.9228830933570862,0.8300727605819702,0.7517518401145935,0.6663541793823242,0.5834450125694275,0.5001380443572998,0.4166485071182251,0.33381393551826477,0.24920859932899475,0.16771917045116425,0.08264345675706863,-2.3043426722324512e-7,-0.08264380693435669,-0.16771942377090454,-0.2492089569568634,-0.3338142931461334,-0.4166487753391266,-0.5001384019851685,-0.5834453105926514,-0.6663547158241272,-0.7517521977424622,-0.8300730586051941,-0.9228836894035339,-0.9787084460258484,-0.9228830933570862,-0.8300726413726807,-0.751751720905304,-0.6663541793823242,-0.5834448933601379,-0.5001379251480103,-0.4166485071182251,-0.3338138163089752,-0.2492084950208664,-0.16771917045116425,-0.08264333754777908,2.3043426722324512e-7,0.08264389634132385,0.16771958768367767,0.2492089569568634", "screen": {"width": 450, "height": 975, "availWidth": 450, "availHeight": 975, "colorDepth": 24, "pixelDepth": 24, "orientation": "portrait-primary", "devicePixelRatio": 1.600000023841858, "innerWidth": 450, "innerHeight": 870, "outerWidth": 450, "outerHeight": 871}, "system": {"platform": "Linux armv8l", "userAgent": "Mozilla/5.0 (Linux; Android 11; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.63 Mobile Safari/537.36 Telegram-Android/11.13.2 (Samsung SM-M107F; Android 11; SDK 30; AVERAGE)", "language": "en", "languages": "en,en-GB,en-US", "hardwareConcurrency": 8, "deviceMemory": 2, "maxTouchPoints": 5, "cookieEnabled": true, "doNotTrack": "unknown", "onLine": true}, "navigator": {"plugins": "", "mimeTypes": "", "vendor": "Google Inc.", "product": "Gecko", "productSub": "20030107", "appName": "Netscape", "appVersion": "5.0 (Linux; Android 11; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.63 Mobile Safari/537.36 Telegram-Android/11.13.2 (Samsung SM-M107F; Android 11; SDK 30; AVERAGE)"}, "timezone": {"timezone": "Africa/Addis_Ababa", "timezoneOffset": -180, "locale": "en", "calendar": "gregory", "numberingSystem": "latn"}, "fonts": "Arial,Helvetica,Times New Roman,Courier New,Verdana,Georgia,Palatino,Tahoma"}, "fingerprint_registered_at": 1752404916, "fingerprint_ip": "**************", "fraud_detected": true, "fraud_reason": "suspicious_screen_resolution, no_plugins", "fraud_detected_at": 1752404916, "id": 375105832, "referrals_count": 0, "joined": 1752404925, "suspicious_activity": 0, "blocked": false}, "2138372954": {"balance": 0, "total_earned": 0, "withdrawals": [], "withdrawal_log": [], "referrer_id": null, "referrals": [], "referral_earnings": 0, "first_name": "<PERSON>", "last_name": "<PERSON><PERSON>", "username": "Dosaida", "language": "en", "registered_at": 1752405182, "last_activity": 1752405182, "suspicious_activity_count": 0, "withdrawals_count": 0, "device_fingerprint": "048173120d52727c9577259ce2e93274afb126647db4b4cbcbed23999b7a5ada", "fingerprint_components": {"canvas": "data:image/png;base64,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", "webgl": {"vendor": "WebKit", "renderer": "WebKit WebGL", "version": "WebGL 1.0 (OpenGL ES 2.0 Chromium)", "shadingLanguageVersion": "WebGL GLSL ES 1.0 (OpenGL ES GLSL ES 1.0 Chromium)", "unmaskedVendor": "Imagination Technologies", "unmaskedRenderer": "PowerVR SGX Auckland", "extensions": "ANGLE_instanced_arrays,EXT_blend_minmax,EXT_color_buffer_half_float,EXT_float_blend,EXT_shader_texture_lod,OES_element_index_uint,OES_fbo_render_mipmap,OES_standard_derivatives,OES_texture_float,OES_texture_half_float,OES_vertex_array_object,WEBGL_color_buffer_float,WEBGL_compressed_texture_astc,WEBGL_compressed_texture_etc,WEBGL_compressed_texture_etc1,WEBGL_compressed_texture_pvrtc,WEBGL_debug_renderer_info,WEBGL_debug_shaders,WEBGL_depth_texture,WEBGL_draw_buffers,WEBGL_lose_context,WEBGL_multi_draw", "maxTextureSize": 4096, "maxViewportDims": "4096,4096"}, "audio": "0,0.08264365047216415,0.16771934926509857,0.2492087185382843,0.3338141441345215,0.4166487157344818,0.5001381635665894,0.5834451913833618,0.6663544178009033,0.7517519593238831,0.8300729393959045,0.9228833317756653,0.9787084460258484,0.9228832721710205,0.8300728797912598,0.7517519593238831,0.6663542985916138,0.583445131778717,0.5001381635665894,0.41664862632751465,0.3338140547275543,0.2492087185382843,0.1677192896604538,0.08264358341693878,0,-0.08264380693435669,-0.16771942377090454,-0.2492087185382843,-0.3338142931461334,-0.4166487753391266,-0.5001381635665894,-0.5834453105926514,-0.6663544774055481,-0.7517519593238831,-0.8300730586051941,-0.9228834509849548,-0.9787084460258484,-0.9228830933570862,-0.8300728797912598,-0.7517519593238831,-0.6663541793823242,-0.583445131778717,-0.5001381635665894,-0.4166485071182251,-0.3338140547275543,-0.2492087185382843,-0.16771917045116425,-0.08264358341693878,1.1521713361162256e-7,0.0826437696814537,0.16771946847438812,0.24920883774757385,0.33381426334381104,0.41664883494377136,0.5001382827758789,0.5834453105926514,0.6663545370101929,0.7517520785331726,0.8300730586051941,0.9228835105895996,0.9787084460258484,0.9228830933570862,0.8300727605819702,0.7517518401145935,0.6663541793823242,0.5834450125694275,0.5001380443572998,0.4166485071182251,0.33381393551826477,0.24920859932899475,0.16771917045116425,0.08264345675706863,-2.3043426722324512e-7,-0.08264380693435669,-0.16771942377090454,-0.2492089569568634,-0.3338142931461334,-0.4166487753391266,-0.5001384019851685,-0.5834453105926514,-0.6663547158241272,-0.7517521977424622,-0.8300730586051941,-0.9228836894035339,-0.9787084460258484,-0.9228830933570862,-0.8300726413726807,-0.751751720905304,-0.6663541793823242,-0.5834448933601379,-0.5001379251480103,-0.4166485071182251,-0.3338138163089752,-0.2492084950208664,-0.16771917045116425,-0.08264333754777908,2.3043426722324512e-7,0.08264389634132385,0.16771958768367767,0.2492089569568634", "screen": {"width": 424, "height": 942, "availWidth": 424, "availHeight": 942, "colorDepth": 24, "pixelDepth": 24, "orientation": "portrait-primary", "devicePixelRatio": 1.274999976158142, "innerWidth": 423, "innerHeight": 812, "outerWidth": 424, "outerHeight": 813}, "system": {"platform": "Linux armv7l", "userAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.7151.118 Mobile Safari/537.36 Telegram-Android/11.13.2 (Tecno mobile limited TECNO BC1s; Android 10; SDK 29; LOW)", "language": "en", "languages": "en,en-US", "hardwareConcurrency": 4, "deviceMemory": 2, "maxTouchPoints": 5, "cookieEnabled": true, "doNotTrack": "unknown", "onLine": true}, "navigator": {"plugins": "", "mimeTypes": "", "vendor": "Google Inc.", "product": "Gecko", "productSub": "20030107", "appName": "Netscape", "appVersion": "5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.7151.118 Mobile Safari/537.36 Telegram-Android/11.13.2 (Tecno mobile limited TECNO BC1s; Android 10; SDK 29; LOW)"}, "timezone": {"timezone": "Asia/Manila", "timezoneOffset": -480, "locale": "en-US", "calendar": "gregory", "numberingSystem": "latn"}, "fonts": "Arial,Helvetica,Times New Roman,Courier New,Verdana,Georgia,Palatino,Tahoma"}, "fingerprint_registered_at": 1752405230, "fingerprint_ip": "*************", "fraud_detected": true, "fraud_reason": "suspicious_screen_resolution, no_plugins", "fraud_detected_at": 1752405230}, "7490319776": {"id": 7490319776, "balance": 0, "total_earned": 0, "joined": 1752406479, "referrer_id": null, "referrals": [], "referrals_count": 0, "referral_earnings": 0, "withdrawals": [], "withdrawal_log": [], "withdrawals_count": 0, "username": "", "first_name": "<PERSON>", "last_name": "", "language": "en", "registered_at": 1752406479, "last_activity": 1752406479, "suspicious_activity": 0, "suspicious_activity_count": 0, "blocked": false, "device_fingerprint": "a0742217d73b1ec9ce66b157ce3a55c4c695f9e87a0bf9f601aa4388af99d612", "fingerprint_components": {"canvas": "data:image/png;base64,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", "webgl": {"vendor": "WebKit", "renderer": "WebKit WebGL", "version": "WebGL 1.0 (OpenGL ES 2.0 Chromium)", "shadingLanguageVersion": "WebGL GLSL ES 1.0 (OpenGL ES GLSL ES 1.0 Chromium)", "unmaskedVendor": "Imagination Technologies", "unmaskedRenderer": "PowerVR SGX Clark", "extensions": "ANGLE_instanced_arrays,EXT_blend_minmax,EXT_color_buffer_half_float,EXT_float_blend,EXT_shader_texture_lod,OES_element_index_uint,OES_fbo_render_mipmap,OES_standard_derivatives,OES_texture_float,OES_texture_half_float,OES_vertex_array_object,WEBGL_color_buffer_float,WEBGL_compressed_texture_astc,WEBGL_compressed_texture_etc,WEBGL_compressed_texture_etc1,WEBGL_compressed_texture_pvrtc,WEBGL_debug_renderer_info,WEBGL_debug_shaders,WEBGL_depth_texture,WEBGL_draw_buffers,WEBGL_lose_context,WEBGL_multi_draw", "maxTextureSize": 4096, "maxViewportDims": "4096,4096"}, "audio": "0,0.08264365047216415,0.16771934926509857,0.2492087185382843,0.3338141441345215,0.4166487157344818,0.5001381635665894,0.5834451913833618,0.6663544178009033,0.7517519593238831,0.8300729393959045,0.9228833317756653,0.9787084460258484,0.9228832721710205,0.8300728797912598,0.7517519593238831,0.6663542985916138,0.583445131778717,0.5001381635665894,0.41664862632751465,0.3338140547275543,0.2492087185382843,0.1677192896604538,0.08264358341693878,0,-0.08264380693435669,-0.16771942377090454,-0.2492087185382843,-0.3338142931461334,-0.4166487753391266,-0.5001381635665894,-0.5834453105926514,-0.6663544774055481,-0.7517519593238831,-0.8300730586051941,-0.9228834509849548,-0.9787084460258484,-0.9228830933570862,-0.8300728797912598,-0.7517519593238831,-0.6663541793823242,-0.583445131778717,-0.5001381635665894,-0.4166485071182251,-0.3338140547275543,-0.2492087185382843,-0.16771917045116425,-0.08264358341693878,1.1521713361162256e-7,0.0826437696814537,0.16771946847438812,0.24920883774757385,0.33381426334381104,0.41664883494377136,0.5001382827758789,0.5834453105926514,0.6663545370101929,0.7517520785331726,0.8300730586051941,0.9228835105895996,0.9787084460258484,0.9228830933570862,0.8300727605819702,0.7517518401145935,0.6663541793823242,0.5834450125694275,0.5001380443572998,0.4166485071182251,0.33381393551826477,0.24920859932899475,0.16771917045116425,0.08264345675706863,-2.3043426722324512e-7,-0.08264380693435669,-0.16771942377090454,-0.2492089569568634,-0.3338142931461334,-0.4166487753391266,-0.5001384019851685,-0.5834453105926514,-0.6663547158241272,-0.7517521977424622,-0.8300730586051941,-0.9228836894035339,-0.9787084460258484,-0.9228830933570862,-0.8300726413726807,-0.751751720905304,-0.6663541793823242,-0.5834448933601379,-0.5001379251480103,-0.4166485071182251,-0.3338138163089752,-0.2492084950208664,-0.16771917045116425,-0.08264333754777908,2.3043426722324512e-7,0.08264389634132385,0.16771958768367767,0.2492089569568634", "screen": {"width": 360, "height": 800, "availWidth": 360, "availHeight": 800, "colorDepth": 24, "pixelDepth": 24, "orientation": "portrait-primary", "devicePixelRatio": 2, "innerWidth": 360, "innerHeight": 664, "outerWidth": 360, "outerHeight": 664}, "system": {"platform": "Linux armv7l", "userAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.63 Mobile Safari/537.36 Telegram-Android/11.6.2 (Infinix mobility limited Infinix X657C; Android 10; SDK 29; LOW)", "language": "en", "languages": "en,en-US", "hardwareConcurrency": 4, "deviceMemory": 2, "maxTouchPoints": 5, "cookieEnabled": true, "doNotTrack": "unknown", "onLine": true}, "navigator": {"plugins": "", "mimeTypes": "", "vendor": "Google Inc.", "product": "Gecko", "productSub": "20030107", "appName": "Netscape", "appVersion": "5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.63 Mobile Safari/537.36 Telegram-Android/11.6.2 (Infinix mobility limited Infinix X657C; Android 10; SDK 29; LOW)"}, "timezone": {"timezone": "Africa/Lagos", "timezoneOffset": -60, "locale": "en-US", "calendar": "gregory", "numberingSystem": "latn"}, "fonts": "Arial,Helvetica,Times New Roman,Courier New,Verdana,Georgia,Palatino,Tahoma"}, "fingerprint_registered_at": 1752406479, "fingerprint_ip": "**************", "fraud_detected": true, "fraud_reason": "suspicious_screen_resolution, no_plugins", "fraud_detected_at": 1752406479}, "7095624825": {"id": 7095624825, "balance": 0, "total_earned": 0, "joined": 1752406571, "referrer_id": null, "referrals": [], "referrals_count": 0, "referral_earnings": 0, "withdrawals": [], "withdrawal_log": [], "withdrawals_count": 0, "username": "", "first_name": "Cyndy▪️", "last_name": "", "language": "en", "registered_at": 1752406571, "last_activity": 1752406571, "suspicious_activity": 0, "suspicious_activity_count": 0, "blocked": false, "device_fingerprint": "a0742217d73b1ec9ce66b157ce3a55c4c695f9e87a0bf9f601aa4388af99d612", "fingerprint_components": {"canvas": "data:image/png;base64,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", "webgl": {"vendor": "WebKit", "renderer": "WebKit WebGL", "version": "WebGL 1.0 (OpenGL ES 2.0 Chromium)", "shadingLanguageVersion": "WebGL GLSL ES 1.0 (OpenGL ES GLSL ES 1.0 Chromium)", "unmaskedVendor": "Imagination Technologies", "unmaskedRenderer": "PowerVR SGX Clark", "extensions": "ANGLE_instanced_arrays,EXT_blend_minmax,EXT_color_buffer_half_float,EXT_float_blend,EXT_shader_texture_lod,OES_element_index_uint,OES_fbo_render_mipmap,OES_standard_derivatives,OES_texture_float,OES_texture_half_float,OES_vertex_array_object,WEBGL_color_buffer_float,WEBGL_compressed_texture_astc,WEBGL_compressed_texture_etc,WEBGL_compressed_texture_etc1,WEBGL_compressed_texture_pvrtc,WEBGL_debug_renderer_info,WEBGL_debug_shaders,WEBGL_depth_texture,WEBGL_draw_buffers,WEBGL_lose_context,WEBGL_multi_draw", "maxTextureSize": 4096, "maxViewportDims": "4096,4096"}, "audio": "0,0.08264365047216415,0.16771934926509857,0.2492087185382843,0.3338141441345215,0.4166487157344818,0.5001381635665894,0.5834451913833618,0.6663544178009033,0.7517519593238831,0.8300729393959045,0.9228833317756653,0.9787084460258484,0.9228832721710205,0.8300728797912598,0.7517519593238831,0.6663542985916138,0.583445131778717,0.5001381635665894,0.41664862632751465,0.3338140547275543,0.2492087185382843,0.1677192896604538,0.08264358341693878,0,-0.08264380693435669,-0.16771942377090454,-0.2492087185382843,-0.3338142931461334,-0.4166487753391266,-0.5001381635665894,-0.5834453105926514,-0.6663544774055481,-0.7517519593238831,-0.8300730586051941,-0.9228834509849548,-0.9787084460258484,-0.9228830933570862,-0.8300728797912598,-0.7517519593238831,-0.6663541793823242,-0.583445131778717,-0.5001381635665894,-0.4166485071182251,-0.3338140547275543,-0.2492087185382843,-0.16771917045116425,-0.08264358341693878,1.1521713361162256e-7,0.0826437696814537,0.16771946847438812,0.24920883774757385,0.33381426334381104,0.41664883494377136,0.5001382827758789,0.5834453105926514,0.6663545370101929,0.7517520785331726,0.8300730586051941,0.9228835105895996,0.9787084460258484,0.9228830933570862,0.8300727605819702,0.7517518401145935,0.6663541793823242,0.5834450125694275,0.5001380443572998,0.4166485071182251,0.33381393551826477,0.24920859932899475,0.16771917045116425,0.08264345675706863,-2.3043426722324512e-7,-0.08264380693435669,-0.16771942377090454,-0.2492089569568634,-0.3338142931461334,-0.4166487753391266,-0.5001384019851685,-0.5834453105926514,-0.6663547158241272,-0.7517521977424622,-0.8300730586051941,-0.9228836894035339,-0.9787084460258484,-0.9228830933570862,-0.8300726413726807,-0.751751720905304,-0.6663541793823242,-0.5834448933601379,-0.5001379251480103,-0.4166485071182251,-0.3338138163089752,-0.2492084950208664,-0.16771917045116425,-0.08264333754777908,2.3043426722324512e-7,0.08264389634132385,0.16771958768367767,0.2492089569568634", "screen": {"width": 360, "height": 800, "availWidth": 360, "availHeight": 800, "colorDepth": 24, "pixelDepth": 24, "orientation": "portrait-primary", "devicePixelRatio": 2, "innerWidth": 360, "innerHeight": 664, "outerWidth": 360, "outerHeight": 664}, "system": {"platform": "Linux armv7l", "userAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.63 Mobile Safari/537.36 Telegram-Android/11.6.2 (Infinix mobility limited Infinix X657C; Android 10; SDK 29; LOW)", "language": "en", "languages": "en,en-US", "hardwareConcurrency": 4, "deviceMemory": 2, "maxTouchPoints": 5, "cookieEnabled": true, "doNotTrack": "unknown", "onLine": true}, "navigator": {"plugins": "", "mimeTypes": "", "vendor": "Google Inc.", "product": "Gecko", "productSub": "20030107", "appName": "Netscape", "appVersion": "5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.63 Mobile Safari/537.36 Telegram-Android/11.6.2 (Infinix mobility limited Infinix X657C; Android 10; SDK 29; LOW)"}, "timezone": {"timezone": "Africa/Lagos", "timezoneOffset": -60, "locale": "en-US", "calendar": "gregory", "numberingSystem": "latn"}, "fonts": "Arial,Helvetica,Times New Roman,Courier New,Verdana,Georgia,Palatino,Tahoma"}, "fingerprint_registered_at": 1752406571, "fingerprint_ip": "**************", "fraud_detected": true, "fraud_reason": "duplicate_fingerprint, suspicious_screen_resolution, no_plugins", "fraud_detected_at": 1752406571}, "1400316876": {"balance": 116, "total_earned": 116, "withdrawals": [], "withdrawal_log": [], "referrer_id": null, "referrals": [], "referral_earnings": 0, "first_name": "<PERSON>", "last_name": "Damina", "username": "Mahidi92", "language": "en", "registered_at": 1752406578, "last_activity": 1752406578, "suspicious_activity_count": 0, "withdrawals_count": 0, "device_fingerprint": "1300b06e31cbea3cfe273ffcf89932940e63fdc28ff25749a15068cca751610e", "fingerprint_components": {"canvas": "data:image/png;base64,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", "webgl": {"vendor": "WebKit", "renderer": "WebKit WebGL", "version": "WebGL 1.0 (OpenGL ES 2.0 Chromium)", "shadingLanguageVersion": "WebGL GLSL ES 1.0 (OpenGL ES GLSL ES 1.0 Chromium)", "unmaskedVendor": "Imagination Technologies", "unmaskedRenderer": "PowerVR Rogue GE8100", "extensions": "ANGLE_instanced_arrays,EXT_blend_minmax,EXT_float_blend,EXT_shader_texture_lod,EXT_texture_filter_anisotropic,OES_element_index_uint,OES_fbo_render_mipmap,OES_standard_derivatives,OES_texture_float,OES_texture_half_float,OES_vertex_array_object,WEBGL_color_buffer_float,WEBGL_compressed_texture_astc,WEBGL_compressed_texture_etc,WEBGL_compressed_texture_etc1,WEBGL_compressed_texture_pvrtc,WEBGL_debug_renderer_info,WEBGL_debug_shaders,WEBGL_depth_texture,WEBGL_draw_buffers,WEBGL_lose_context,WEBGL_multi_draw", "maxTextureSize": 4096, "maxViewportDims": "4096,4096"}, "audio": "audio_timeout", "screen": {"width": 360, "height": 750, "availWidth": 360, "availHeight": 750, "colorDepth": 24, "pixelDepth": 24, "orientation": "portrait-primary", "devicePixelRatio": 2, "innerWidth": 360, "innerHeight": 610, "outerWidth": 360, "outerHeight": 610}, "system": {"platform": "Linux armv7l", "userAgent": "Mozilla/5.0 (Linux; Android 8.1.0; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.6943.121 Mobile Safari/537.36 Telegram-Android/11.5.5 (Tecno mobile limited TECNO LB7; Android 8.1.0; SDK 27; LOW)", "language": "en", "languages": "en,en-US", "hardwareConcurrency": 4, "deviceMemory": 2, "maxTouchPoints": 5, "cookieEnabled": true, "doNotTrack": "unknown", "onLine": true}, "navigator": {"plugins": "", "mimeTypes": "", "vendor": "Google Inc.", "product": "Gecko", "productSub": "20030107", "appName": "Netscape", "appVersion": "5.0 (Linux; Android 8.1.0; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.6943.121 Mobile Safari/537.36 Telegram-Android/11.5.5 (Tecno mobile limited TECNO LB7; Android 8.1.0; SDK 27; LOW)"}, "timezone": {"timezone": "Africa/Lagos", "timezoneOffset": -60, "locale": "en", "calendar": "gregory", "numberingSystem": "latn"}, "fonts": "Arial,Helvetica,Times New Roman,Courier New,Verdana,Georgia,Palatino,Tahoma"}, "fingerprint_registered_at": 1752408594, "fingerprint_ip": "*************", "fraud_detected": true, "fraud_reason": "suspicious_screen_resolution, no_plugins", "fraud_detected_at": 1752408594, "id": 1400316876, "referrals_count": 0, "joined": 1752406655, "suspicious_activity": 0, "blocked": false, "ad_views_log": [{"time": 1752406713, "reward": 10, "type": "native_banner"}, {"time": 1752406758, "reward": 10, "type": "native_banner"}, {"time": 1752406800, "reward": 10, "type": "native_banner"}, {"time": 1752406840, "reward": 10, "type": "native_banner"}, {"time": 1752406882, "reward": 10, "type": "native_banner"}, {"time": 1752406919, "reward": 10, "type": "native_banner"}, {"time": 1752406958, "reward": 10, "type": "native_banner"}, {"time": 1752407012, "reward": 10, "type": "native_banner"}, {"time": 1752407117, "reward": 10, "type": "native_banner"}, {"time": 1752407161, "reward": 10, "type": "native_banner"}, {"time": 1752407206, "reward": 8, "type": "interstitial"}, {"time": 1752407262, "reward": 8, "type": "interstitial"}]}}