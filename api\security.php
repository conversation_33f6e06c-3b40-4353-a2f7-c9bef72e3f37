<?php
/**
 * api/security.php
 * Функции безопасности для защиты от мошенничества и накрутки монет
 */

// Устанавливаем часовой пояс на UTC
date_default_timezone_set('UTC');

// Константы для ограничений
define('MAX_AD_VIEWS_PER_HOUR', 300); // Общий лимит просмотров рекламы в час (увеличен для большого трафика)
define('MAX_AD_VIEWS_PER_DAY', 1500); // Общий лимит просмотров рекламы в день (увеличен для большого трафика)
define('MAX_WITHDRAWALS_PER_DAY', 999999); // Лимиты на выводы отключены
define('SUSPICIOUS_ACTIVITY_THRESHOLD', 10); // Порог подозрительной активности (увеличен для большого трафика)
define('MAX_REQUESTS_PER_MINUTE', 60); // Максимальное количество запросов с одного IP в минуту (увеличен для большого трафика)

// Лимиты для каждого типа рекламы отдельно (увеличены для большого трафика)
define('MAX_AD_VIEWS_PER_TYPE_PER_DAY', 100); // Лимит для каждого типа рекламы в день (было 20, стало 100)
define('MAX_AD_VIEWS_PER_TYPE_PER_HOUR', 50); // Лимит для каждого типа рекламы в час (было 20, стало 50)

/**
 * Проверяет лимит запросов с одного IP адреса
 *
 * @param string $ip IP адрес клиента
 * @return bool true, если лимит не превышен, false в противном случае
 */
function checkIpRateLimit($ip) {
    $rateLimitFile = __DIR__ . '/rate_limit.json';
    $currentTime = time();
    $minuteAgo = $currentTime - 60;

    // Загружаем данные о запросах
    $rateLimitData = [];
    if (file_exists($rateLimitFile)) {
        $jsonData = file_get_contents($rateLimitFile);
        if ($jsonData !== false) {
            $rateLimitData = json_decode($jsonData, true) ?: [];
        }
    }

    // Инициализируем данные для IP, если их нет
    if (!isset($rateLimitData[$ip])) {
        $rateLimitData[$ip] = [];
    }

    // Фильтруем запросы за последнюю минуту
    $rateLimitData[$ip] = array_filter($rateLimitData[$ip], function($timestamp) use ($minuteAgo) {
        return $timestamp >= $minuteAgo;
    });

    // Проверяем лимит
    if (count($rateLimitData[$ip]) >= MAX_REQUESTS_PER_MINUTE) {
        error_log("security WARNING: IP {$ip} превысил лимит запросов в минуту");
        return false;
    }

    // Добавляем новый запрос
    $rateLimitData[$ip][] = $currentTime;

    // Очищаем старые данные (старше часа)
    $hourAgo = $currentTime - 3600;
    foreach ($rateLimitData as $checkIp => $timestamps) {
        $rateLimitData[$checkIp] = array_filter($timestamps, function($timestamp) use ($hourAgo) {
            return $timestamp >= $hourAgo;
        });

        // Удаляем пустые записи
        if (empty($rateLimitData[$checkIp])) {
            unset($rateLimitData[$checkIp]);
        }
    }

    // Сохраняем обновленные данные
    file_put_contents($rateLimitFile, json_encode($rateLimitData), LOCK_EX);

    return true;
}

/**
 * Проверяет, не превышен ли лимит просмотров рекламы определенного типа
 *
 * @param int $userId ID пользователя
 * @param string $adType Тип рекламы (native_banner, rewarded_video, interstitial)
 * @param array $userData Данные всех пользователей
 * @return bool true, если лимит не превышен, false в противном случае
 */
function checkAdViewLimitByType($userId, $adType, &$userData) {
    // Проверяем, отключены ли лимиты рекламы
    if (areAdLimitsDisabled()) {
        error_log("security INFO: Лимиты рекламы отключены - пропускаем проверку для пользователя {$userId}, тип {$adType}");
        return true;
    }

    if (!isset($userData[$userId])) {
        error_log("security ERROR: Пользователь {$userId} не найден при проверке лимита просмотров типа {$adType}");
        return false;
    }

    // Инициализируем массив для отслеживания просмотров рекламы по типам, если его еще нет
    if (!isset($userData[$userId]['ad_views_by_type'])) {
        $userData[$userId]['ad_views_by_type'] = [];
    }

    if (!isset($userData[$userId]['ad_views_by_type'][$adType])) {
        $userData[$userId]['ad_views_by_type'][$adType] = [];
    }

    $currentTime = time();
    $hourAgo = $currentTime - 3600;
    $dayAgo = $currentTime - 86400;

    // Фильтруем просмотры данного типа за последний час
    $hourViews = array_filter($userData[$userId]['ad_views_by_type'][$adType], function($timestamp) use ($hourAgo) {
        return $timestamp >= $hourAgo;
    });

    // Фильтруем просмотры данного типа за последний день
    $dayViews = array_filter($userData[$userId]['ad_views_by_type'][$adType], function($timestamp) use ($dayAgo) {
        return $timestamp >= $dayAgo;
    });

    // Очищаем старые записи (старше суток)
    $userData[$userId]['ad_views_by_type'][$adType] = $dayViews;

    // Динамические лимиты для пиковых часов (UTC)
    $currentHour = intval(gmdate('H')); // UTC время
    $peakHours = [10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21]; // Пиковые часы UTC
    $isPeakTime = in_array($currentHour, $peakHours);

    // Увеличиваем лимиты в пиковые часы
    $hourlyLimit = $isPeakTime ? (MAX_AD_VIEWS_PER_TYPE_PER_HOUR * 2) : MAX_AD_VIEWS_PER_TYPE_PER_HOUR;
    $dailyLimit = $isPeakTime ? (MAX_AD_VIEWS_PER_TYPE_PER_DAY * 1.5) : MAX_AD_VIEWS_PER_TYPE_PER_DAY;

    // Проверяем лимиты для данного типа рекламы
    if (count($hourViews) >= $hourlyLimit) {
        $peakInfo = $isPeakTime ? " (пиковое время)" : "";
        error_log("security WARNING: Пользователь {$userId} превысил часовой лимит просмотров рекламы типа {$adType} (" . count($hourViews) . "/{$hourlyLimit}){$peakInfo}");
        return false;
    }

    if (count($dayViews) >= $dailyLimit) {
        $peakInfo = $isPeakTime ? " (пиковое время)" : "";
        error_log("security WARNING: Пользователь {$userId} превысил дневной лимит просмотров рекламы типа {$adType} (" . count($dayViews) . "/{$dailyLimit}){$peakInfo}");
        return false;
    }

    // Добавляем новый просмотр в лог для данного типа
    $userData[$userId]['ad_views_by_type'][$adType][] = $currentTime;

    $peakInfo = $isPeakTime ? " (пиковое время x1.5)" : "";
    error_log("security INFO: Просмотр рекламы типа {$adType} засчитан для пользователя {$userId}. Сегодня: " . (count($dayViews) + 1) . "/{$dailyLimit}{$peakInfo}");

    return true;
}

/**
 * Проверяет, не превышен ли общий лимит просмотров рекламы (для совместимости)
 *
 * @param int $userId ID пользователя
 * @param array $userData Данные всех пользователей
 * @return bool true, если лимит не превышен, false в противном случае
 */
function checkAdViewLimit($userId, &$userData) {
    // Проверяем, отключены ли лимиты рекламы
    if (areAdLimitsDisabled()) {
        error_log("security INFO: Лимиты рекламы отключены - пропускаем общую проверку для пользователя {$userId}");
        return true;
    }

    if (!isset($userData[$userId])) {
        error_log("security ERROR: Пользователь {$userId} не найден при проверке лимита просмотров");
        return false;
    }

    // Инициализируем массив для отслеживания просмотров рекламы, если его еще нет
    if (!isset($userData[$userId]['ad_views_log'])) {
        $userData[$userId]['ad_views_log'] = [];
    }

    $currentTime = time();
    $hourAgo = $currentTime - 3600;
    $dayAgo = $currentTime - 86400;

    // Фильтруем просмотры за последний час
    $hourViews = array_filter($userData[$userId]['ad_views_log'], function($timestamp) use ($hourAgo) {
        return $timestamp >= $hourAgo;
    });

    // Фильтруем просмотры за последний день
    $dayViews = array_filter($userData[$userId]['ad_views_log'], function($timestamp) use ($dayAgo) {
        return $timestamp >= $dayAgo;
    });

    // Очищаем старые записи (старше суток)
    $userData[$userId]['ad_views_log'] = $dayViews;

    // Динамические лимиты для пиковых часов (общие лимиты)
    $currentHour = intval(gmdate('H')); // UTC время
    $peakHours = [10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21]; // Пиковые часы UTC
    $isPeakTime = in_array($currentHour, $peakHours);

    // Увеличиваем общие лимиты в пиковые часы
    $hourlyLimit = $isPeakTime ? (MAX_AD_VIEWS_PER_HOUR * 2) : MAX_AD_VIEWS_PER_HOUR;
    $dailyLimit = $isPeakTime ? (MAX_AD_VIEWS_PER_DAY * 1.5) : MAX_AD_VIEWS_PER_DAY;

    // Проверяем лимиты
    if (count($hourViews) >= $hourlyLimit) {
        $peakInfo = $isPeakTime ? " (пиковое время)" : "";
        error_log("security WARNING: Пользователь {$userId} превысил общий часовой лимит просмотров рекламы (" . count($hourViews) . "/{$hourlyLimit}){$peakInfo}");
        return false;
    }

    if (count($dayViews) >= $dailyLimit) {
        $peakInfo = $isPeakTime ? " (пиковое время)" : "";
        error_log("security WARNING: Пользователь {$userId} превысил общий дневной лимит просмотров рекламы (" . count($dayViews) . "/{$dailyLimit}){$peakInfo}");
        return false;
    }

    // Добавляем новый просмотр в лог
    $userData[$userId]['ad_views_log'][] = $currentTime;

    return true;
}

/**
 * Проверяет, не превышен ли лимит выводов средств
 * 
 * @param int $userId ID пользователя
 * @param array $userData Данные всех пользователей
 * @return bool true, если лимит не превышен, false в противном случае
 */
function checkWithdrawalLimit($userId, &$userData) {
    if (!isset($userData[$userId])) {
        error_log("security ERROR: Пользователь {$userId} не найден при проверке лимита выводов");
        return false;
    }
    
    // Инициализируем массив для отслеживания выводов, если его еще нет
    if (!isset($userData[$userId]['withdrawal_log'])) {
        $userData[$userId]['withdrawal_log'] = [];
    }
    
    $currentTime = time();
    $dayAgo = $currentTime - 86400;
    
    // Фильтруем выводы за последний день
    $dayWithdrawals = array_filter($userData[$userId]['withdrawal_log'], function($timestamp) use ($dayAgo) {
        return $timestamp >= $dayAgo;
    });
    
    // Очищаем старые записи (старше суток)
    $userData[$userId]['withdrawal_log'] = $dayWithdrawals;
    
    // Проверяем лимит
    if (count($dayWithdrawals) >= MAX_WITHDRAWALS_PER_DAY) {
        error_log("security WARNING: Пользователь {$userId} превысил дневной лимит выводов средств");
        return false;
    }
    
    // Добавляем новый вывод в лог
    $userData[$userId]['withdrawal_log'][] = $currentTime;
    
    return true;
}

/**
 * Проверяет баланс пользователя перед выводом средств
 * 
 * @param int $userId ID пользователя
 * @param int $amount Сумма для вывода
 * @param array $userData Данные всех пользователей
 * @return bool true, если баланс достаточен, false в противном случае
 */
function verifyBalance($userId, $amount, &$userData) {
    if (!isset($userData[$userId]) || !isset($userData[$userId]['balance'])) {
        error_log("security ERROR: Пользователь {$userId} не найден или нет баланса при проверке баланса");
        return false;
    }
    
    $balance = $userData[$userId]['balance'];
    
    // Проверяем, достаточно ли средств
    if ($balance < $amount) {
        error_log("security WARNING: Недостаточно средств у пользователя {$userId}. Запрошено: {$amount}, Доступно: {$balance}");
        return false;
    }
    
    // ИСПРАВЛЕНИЕ: Убираем избыточную проверку total_earned vs total_withdrawn
    // Баланс пользователя уже корректно отражает доступные для вывода средства
    // Проверка total_earned может блокировать легитимные выводы из-за рассинхронизации данных

    error_log("security INFO: Проверка баланса пройдена для пользователя {$userId}. Запрошено: {$amount}, Доступно: {$balance}");

    return true;
}

/**
 * Записывает операцию в журнал аудита
 * 
 * @param string $operation Тип операции
 * @param int $userId ID пользователя
 * @param array $data Дополнительные данные
 * @return bool true в случае успеха, false при ошибке
 */
function logAuditEvent($operation, $userId, $data = []) {
    $logFile = __DIR__ . '/audit.log';
    
    $logEntry = [
        'timestamp' => time(),
        'operation' => $operation,
        'user_id' => $userId,
        'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
        'data' => $data
    ];
    
    $logLine = gmdate('Y-m-d H:i:s') . ' | ' . json_encode($logEntry) . PHP_EOL;
    
    return file_put_contents($logFile, $logLine, FILE_APPEND | LOCK_EX) !== false;
}

/**
 * Проверяет, не является ли активность пользователя подозрительной
 * 
 * @param int $userId ID пользователя
 * @param array $userData Данные всех пользователей
 * @return bool true, если активность не подозрительная, false в противном случае
 */
function checkSuspiciousActivity($userId, &$userData) {
    if (!isset($userData[$userId])) {
        error_log("security ERROR: Пользователь {$userId} не найден при проверке подозрительной активности");
        return false;
    }
    
    // Инициализируем счетчик подозрительной активности, если его еще нет
    if (!isset($userData[$userId]['suspicious_activity'])) {
        $userData[$userId]['suspicious_activity'] = 0;
    }
    
    // Если счетчик превышает порог, блокируем пользователя
    if ($userData[$userId]['suspicious_activity'] >= SUSPICIOUS_ACTIVITY_THRESHOLD) {
        if (!isset($userData[$userId]['blocked']) || !$userData[$userId]['blocked']) {
            $userData[$userId]['blocked'] = true;
            $userData[$userId]['blocked_at'] = time();
            error_log("security ALERT: Пользователь {$userId} заблокирован из-за подозрительной активности");
        }
        return false;
    }
    
    return true;
}

/**
 * Увеличивает счетчик подозрительной активности пользователя
 * 
 * @param int $userId ID пользователя
 * @param array $userData Данные всех пользователей
 * @param string $reason Причина увеличения счетчика
 * @return void
 */
function incrementSuspiciousActivity($userId, &$userData, $reason = '') {
    if (!isset($userData[$userId])) {
        error_log("security ERROR: Пользователь {$userId} не найден при увеличении счетчика подозрительной активности");
        return;
    }
    
    // Инициализируем счетчик подозрительной активности, если его еще нет
    if (!isset($userData[$userId]['suspicious_activity'])) {
        $userData[$userId]['suspicious_activity'] = 0;
    }
    
    $userData[$userId]['suspicious_activity']++;
    
    error_log("security WARNING: Увеличен счетчик подозрительной активности для пользователя {$userId}. Новое значение: {$userData[$userId]['suspicious_activity']}. Причина: {$reason}");
    
    // Логируем событие
    logAuditEvent('suspicious_activity', $userId, ['reason' => $reason, 'count' => $userData[$userId]['suspicious_activity']]);
    
    // Если счетчик превысил порог, блокируем пользователя
    if ($userData[$userId]['suspicious_activity'] >= SUSPICIOUS_ACTIVITY_THRESHOLD) {
        $userData[$userId]['blocked'] = true;
        $userData[$userId]['blocked_at'] = time();
        error_log("security ALERT: Пользователь {$userId} заблокирован из-за подозрительной активности");
        
        // Логируем блокировку
        logAuditEvent('user_blocked', $userId, ['reason' => 'suspicious_activity_threshold_reached']);
    }
}

/**
 * Проверяет, отключены ли все лимиты рекламы
 *
 * @return bool true если лимиты отключены
 */
function areAdLimitsDisabled() {
    $settingsFile = __DIR__ . '/admin/ad_limits_settings.json';

    if (!file_exists($settingsFile)) {
        return false;
    }

    $content = file_get_contents($settingsFile);
    $settings = json_decode($content, true);

    return isset($settings['disable_all_ad_limits']) && $settings['disable_all_ad_limits'] === true;
}

/**
 * Получает текущие активные лимиты с учетом пикового времени
 *
 * @return array Массив с информацией о лимитах
 */
function getCurrentLimits() {
    $currentHour = intval(gmdate('H')); // UTC время
    $peakHours = [10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21]; // Пиковые часы UTC
    $isPeakTime = in_array($currentHour, $peakHours);
    $limitsDisabled = areAdLimitsDisabled();

    // Если лимиты отключены, возвращаем очень большие значения
    if ($limitsDisabled) {
        return [
            'is_peak_time' => $isPeakTime,
            'current_hour_utc' => $currentHour,
            'peak_hours' => $peakHours,
            'limits_disabled' => true,
            'limits' => [
                'ad_views_per_type_per_hour' => 999999,
                'ad_views_per_type_per_day' => 999999,
                'ad_views_per_hour' => 999999,
                'ad_views_per_day' => 999999,
                'requests_per_minute' => MAX_REQUESTS_PER_MINUTE, // IP лимиты остаются
                'suspicious_activity_threshold' => SUSPICIOUS_ACTIVITY_THRESHOLD // Блокировка остается
            ],
            'base_limits' => [
                'ad_views_per_type_per_hour' => MAX_AD_VIEWS_PER_TYPE_PER_HOUR,
                'ad_views_per_type_per_day' => MAX_AD_VIEWS_PER_TYPE_PER_DAY,
                'ad_views_per_hour' => MAX_AD_VIEWS_PER_HOUR,
                'ad_views_per_day' => MAX_AD_VIEWS_PER_DAY
            ]
        ];
    }

    // Обычный режим с лимитами
    return [
        'is_peak_time' => $isPeakTime,
        'current_hour_utc' => $currentHour,
        'peak_hours' => $peakHours,
        'limits_disabled' => false,
        'limits' => [
            'ad_views_per_type_per_hour' => $isPeakTime ? (MAX_AD_VIEWS_PER_TYPE_PER_HOUR * 2) : MAX_AD_VIEWS_PER_TYPE_PER_HOUR,
            'ad_views_per_type_per_day' => $isPeakTime ? (MAX_AD_VIEWS_PER_TYPE_PER_DAY * 1.5) : MAX_AD_VIEWS_PER_TYPE_PER_DAY,
            'ad_views_per_hour' => $isPeakTime ? (MAX_AD_VIEWS_PER_HOUR * 2) : MAX_AD_VIEWS_PER_HOUR,
            'ad_views_per_day' => $isPeakTime ? (MAX_AD_VIEWS_PER_DAY * 1.5) : MAX_AD_VIEWS_PER_DAY,
            'requests_per_minute' => MAX_REQUESTS_PER_MINUTE,
            'suspicious_activity_threshold' => SUSPICIOUS_ACTIVITY_THRESHOLD
        ],
        'base_limits' => [
            'ad_views_per_type_per_hour' => MAX_AD_VIEWS_PER_TYPE_PER_HOUR,
            'ad_views_per_type_per_day' => MAX_AD_VIEWS_PER_TYPE_PER_DAY,
            'ad_views_per_hour' => MAX_AD_VIEWS_PER_HOUR,
            'ad_views_per_day' => MAX_AD_VIEWS_PER_DAY
        ]
    ];
}
?>
