<?php
header('Content-Type: application/json');

// Устанавливаем часовой пояс на UTC для всех операций с датой и временем
date_default_timezone_set('UTC');

// Подключаем аутентификацию
require_once __DIR__ . '/auth.php';
session_start();
if (!isAuthenticated()) {
    http_response_code(403);
    echo json_encode(['success' => false, 'error' => 'Unauthorized']);
    exit;
}

// --- Функции ---

function getCountryCode($ip) {
    static $cache = [];
    static $persistent_cache = null;

    // Инициализируем постоянный кэш из файла
    if ($persistent_cache === null) {
        $cache_file = __DIR__ . '/../../database/ip_country_cache.json';
        if (file_exists($cache_file)) {
            $persistent_cache = json_decode(file_get_contents($cache_file), true) ?: [];
        } else {
            $persistent_cache = [];
        }
    }

    // Проверяем кэш в памяти
    if (isset($cache[$ip])) {
        return $cache[$ip];
    }

    // Проверяем постоянный кэш
    if (isset($persistent_cache[$ip])) {
        $cache[$ip] = $persistent_cache[$ip];
        return $persistent_cache[$ip];
    }

    // Проверяем локальные и приватные IP адреса
    if ($ip === '127.0.0.1' || $ip === 'unknown' || $ip === 'localhost' ||
        !filter_var($ip, FILTER_VALIDATE_IP) ||
        filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) === false) {
        $cache[$ip] = 'Unknown';
        return 'Unknown';
    }

    // Ограничиваем количество новых запросов к API за один вызов
    static $api_calls_count = 0;
    if ($api_calls_count >= 5) {
        $cache[$ip] = 'XX';
        return 'XX';
    }

    // Пробуем только один быстрый API
    $url = "http://ip-api.com/json/{$ip}?fields=status,countryCode";

    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 1); // Уменьшили таймаут
    curl_setopt($ch, CURLOPT_TIMEOUT, 2); // Уменьшили таймаут
    curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (compatible; Stats/1.0)');
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    $api_calls_count++;

    if ($response !== false && $httpCode === 200) {
        $data = json_decode($response, true);
        if (isset($data['status']) && $data['status'] === 'success' && isset($data['countryCode'])) {
            $cache[$ip] = $data['countryCode'];
            $persistent_cache[$ip] = $data['countryCode'];

            // Сохраняем в файл кэша (асинхронно)
            $cache_file = __DIR__ . '/../../database/ip_country_cache.json';
            file_put_contents($cache_file, json_encode($persistent_cache, JSON_PRETTY_PRINT));

            return $data['countryCode'];
        }
    }

    // Если API не сработал, возвращаем код по умолчанию
    $cache[$ip] = 'XX';
    return 'XX';
}

function load_json_file($filepath, $limit = null) {
    if (!file_exists($filepath)) return [];
    $content = file_get_contents($filepath);
    if (empty($content)) return [];
    $data = json_decode($content, true);
    if (json_last_error() !== JSON_ERROR_NONE) {
        error_log("JSON decode error in file: $filepath - " . json_last_error_msg());
        return [];
    }

    if (!is_array($data)) return [];

    // Ограничиваем количество записей для производительности
    if ($limit && count($data) > $limit) {
        // Берем последние записи (самые свежие)
        $data = array_slice($data, -$limit);
    }

    return $data;
}

// --- Основная логика ---

// Проверяем кэш результатов
$cache_key = md5(serialize($_GET));
$cache_file = __DIR__ . '/../../database/stats_cache_' . $cache_key . '.json';
$cache_ttl = 300; // 5 минут

if (file_exists($cache_file) && (time() - filemtime($cache_file)) < $cache_ttl) {
    $cached_data = json_decode(file_get_contents($cache_file), true);
    if ($cached_data && isset($cached_data['success'])) {
        echo json_encode($cached_data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
        exit;
    }
}

// Умная загрузка: ограничиваем основные данные, но загружаем все для статистики стран
$clicks_limited = load_json_file(__DIR__ . '/../../database/ad_clicks.json', 1000); // Увеличили лимит
$views_limited = load_json_file(__DIR__ . '/../../database/ad_views.json', 500);   // Увеличили лимит

// Для статистики стран загружаем все данные, но обрабатываем только IP
$clicks_for_countries = load_json_file(__DIR__ . '/../../database/ad_clicks.json');

// Используем ограниченные данные для основной статистики
$clicks = $clicks_limited;
$views = $views_limited;

// Фильтрация
$date_from = !empty($_GET['date_from']) ? strtotime($_GET['date_from'] . ' 00:00:00 UTC') : null;
$date_to = !empty($_GET['date_to']) ? strtotime($_GET['date_to'] . ' 23:59:59 UTC') : null;
$ad_type_filter = $_GET['ad_type'] ?? 'all';

$filter_by_date = fn($entry) => (!$date_from || strtotime($entry['timestamp'] . ' UTC') >= $date_from) && (!$date_to || strtotime($entry['timestamp'] . ' UTC') <= $date_to);
$filter_by_ad_type = fn($entry) => $ad_type_filter === 'all' || ($entry['ad_type'] ?? 'unknown') === $ad_type_filter;

$filtered_clicks = array_filter($clicks, fn($c) => !empty($c['timestamp']) && $filter_by_date($c) && $filter_by_ad_type($c));
$filtered_views = array_filter($views, fn($v) => !empty($v['timestamp']) && $filter_by_date($v) && $filter_by_ad_type($v));

// Агрегация
$stats_by_type = [];
$stats_by_country = [];
$hourly_stats = array_fill(0, 24, ['clicks' => 0, 'views' => 0]);

$ad_types = ['native_banner', 'rewarded_video', 'interstitial'];
foreach ($ad_types as $type) {
    if ($ad_type_filter === 'all' || $ad_type_filter === $type) {
        $stats_by_type[$type] = ['clicks' => 0, 'views' => 0, 'rewards' => 0, 'ctr' => 0];
    }
}

foreach ($filtered_clicks as $click) {
    if (isset($click['click_type']) && $click['click_type'] === 'button_click') {
        $ad_type = $click['ad_type'] ?? 'unknown';
        if (isset($stats_by_type[$ad_type])) $stats_by_type[$ad_type]['clicks']++;

        $hour = (int)gmdate('H', strtotime($click['timestamp'] . ' UTC'));
        $hourly_stats[$hour]['clicks']++;
    }
}

// Отдельная обработка статистики стран с полными данными
$filter_by_date_countries = fn($entry) => (!$date_from || strtotime($entry['timestamp'] . ' UTC') >= $date_from) && (!$date_to || strtotime($entry['timestamp'] . ' UTC') <= $date_to);
$filter_by_ad_type_countries = fn($entry) => $ad_type_filter === 'all' || ($entry['ad_type'] ?? 'unknown') === $ad_type_filter;

$filtered_clicks_countries = array_filter($clicks_for_countries, fn($c) => !empty($c['timestamp']) && $filter_by_date_countries($c) && $filter_by_ad_type_countries($c));

foreach ($filtered_clicks_countries as $click) {
    if (isset($click['click_type']) && $click['click_type'] === 'button_click') {
        $country = getCountryCode($click['ip'] ?? 'unknown');
        $stats_by_country[$country] = ($stats_by_country[$country] ?? 0) + 1;
    }
}

foreach ($filtered_views as $view) {
    $ad_type = $view['ad_type'] ?? 'unknown';
    if (isset($stats_by_type[$ad_type])) {
        $stats_by_type[$ad_type]['views']++;
        $stats_by_type[$ad_type]['rewards'] += $view['reward'] ?? 0;
    }
    $hour = (int)gmdate('H', strtotime($view['timestamp'] . ' UTC'));
    $hourly_stats[$hour]['views']++;
}

foreach ($stats_by_type as &$data) {
    if ($data['clicks'] > 0) $data['ctr'] = round(($data['views'] / $data['clicks']) * 100, 2);
}

arsort($stats_by_country);

$response = [
    'success' => true,
    'stats_by_type' => $stats_by_type,
    'stats_by_country' => $stats_by_country,
    'hourly_stats' => $hourly_stats,
    'last_updated' => gmdate('Y-m-d H:i:s') . ' UTC',
    'data_info' => [
        'clicks_processed' => count($filtered_clicks),
        'views_processed' => count($filtered_views),
        'countries_from_clicks' => count($filtered_clicks_countries),
        'total_countries' => count($stats_by_country)
    ]
];

// Сохраняем результат в кэш
file_put_contents($cache_file, json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));

echo json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);