<?php
/**
 * ЖЕЛЕЗОБЕТОННАЯ СИСТЕМА ЗАЩИТЫ ДАННЫХ ПОЛЬЗОВАТЕЛЕЙ
 * Исключает возможность потери данных при любых обстоятельствах
 */

require_once __DIR__ . '/critical_logger.php';

class SecureDataManager {
    
    private $dataFile;
    private $backupDir;
    private $maxBackups = 10;
    private $minFileSize = 100; // Минимальный размер файла в байтах
    private $lockTimeout = 30; // 30 секунд
    
    public function __construct($dataFile) {
        $this->dataFile = $dataFile;
        $this->backupDir = dirname($dataFile) . '/backups';
        
        // Создаем директорию для бэкапов
        if (!is_dir($this->backupDir)) {
            mkdir($this->backupDir, 0755, true);
        }
    }
    
    /**
     * БЕЗОПАСНОЕ СОХРАНЕНИЕ ДАННЫХ С МНОЖЕСТВЕННОЙ ЗАЩИТОЙ
     */
    public function saveUserDataSecure(array $userData): bool {
        logWarning("SecureDataManager: Начало безопасного сохранения данных", [
            'users_count' => count($userData),
            'file' => $this->dataFile
        ]);
        
        // 1. ВАЛИДАЦИЯ ДАННЫХ
        if (!$this->validateUserData($userData)) {
            logCritical("SecureDataManager: Данные не прошли валидацию - ОТМЕНА СОХРАНЕНИЯ");
            return false;
        }
        
        // 2. СОЗДАНИЕ БЭКАПА ТЕКУЩИХ ДАННЫХ (необязательно)
        $backupCreated = $this->createBackup();
        if (!$backupCreated) {
            logWarning("SecureDataManager: Не удалось создать бэкап, но продолжаем сохранение");
        }
        
        // 3. АТОМАРНАЯ ЗАПИСЬ ЧЕРЕЗ ВРЕМЕННЫЙ ФАЙЛ
        if (!$this->atomicWrite($userData)) {
            logCritical("SecureDataManager: Атомарная запись не удалась");
            if ($backupCreated) {
                logCritical("SecureDataManager: Пытаемся восстановить из бэкапа");
                $this->restoreFromBackup();
            }
            return false;
        }

        // 4. ВЕРИФИКАЦИЯ ЗАПИСАННЫХ ДАННЫХ
        if (!$this->verifyWrittenData($userData)) {
            logCritical("SecureDataManager: Верификация не удалась");
            if ($backupCreated) {
                logCritical("SecureDataManager: Пытаемся восстановить из бэкапа");
                $this->restoreFromBackup();
            }
            return false;
        }
        
        // 5. ОЧИСТКА СТАРЫХ БЭКАПОВ
        $this->cleanupOldBackups();
        
        logWarning("SecureDataManager: Данные успешно сохранены и верифицированы");
        return true;
    }
    
    /**
     * ВАЛИДАЦИЯ ДАННЫХ ПОЛЬЗОВАТЕЛЕЙ
     */
    private function validateUserData(array $userData): bool {
        if (empty($userData)) {
            logError("SecureDataManager: Пустой массив данных");
            return false;
        }
        
        if (count($userData) < 10) {
            logError("SecureDataManager: Подозрительно мало пользователей", ['count' => count($userData)]);
            return false;
        }
        
        // Проверяем структуру данных
        $validUsers = 0;
        foreach ($userData as $userId => $userDetails) {
            if (!is_numeric($userId)) {
                logError("SecureDataManager: Некорректный ID пользователя", ['user_id' => $userId]);
                continue;
            }
            
            if (!is_array($userDetails)) {
                logError("SecureDataManager: Данные пользователя не массив", ['user_id' => $userId]);
                continue;
            }
            
            if (!array_key_exists('balance', $userDetails) || !array_key_exists('referrer_id', $userDetails)) {
                logError("SecureDataManager: Отсутствуют обязательные поля", [
                    'user_id' => $userId,
                    'has_balance' => array_key_exists('balance', $userDetails),
                    'has_referrer_id' => array_key_exists('referrer_id', $userDetails)
                ]);
                continue;
            }
            
            $validUsers++;
        }
        
        if ($validUsers < count($userData) * 0.9) {
            logError("SecureDataManager: Слишком много невалидных пользователей", [
                'total' => count($userData),
                'valid' => $validUsers,
                'percentage' => round($validUsers / count($userData) * 100, 2)
            ]);
            return false;
        }
        
        return true;
    }
    
    /**
     * СОЗДАНИЕ БЭКАПА ТЕКУЩИХ ДАННЫХ (НЕОБЯЗАТЕЛЬНО)
     */
    private function createBackup(): bool {
        try {
            // Убеждаемся, что директория для бэкапов существует
            if (!is_dir($this->backupDir)) {
                if (!mkdir($this->backupDir, 0755, true)) {
                    logWarning("SecureDataManager: Не удалось создать директорию бэкапов", ['dir' => $this->backupDir]);
                    return false;
                }
            }

            if (!file_exists($this->dataFile)) {
                logWarning("SecureDataManager: Основной файл не существует, пропускаем бэкап");
                return true; // Это нормально для первого запуска
            }

            $fileSize = filesize($this->dataFile);
            if ($fileSize < $this->minFileSize) {
                logWarning("SecureDataManager: Файл слишком мал для бэкапа", ['size' => $fileSize]);
                return false;
            }

            // Используем basename файла для создания имени бэкапа
            $baseName = pathinfo($this->dataFile, PATHINFO_FILENAME);
            $backupFile = $this->backupDir . '/' . $baseName . '_backup_' . date('Y-m-d_H-i-s') . '.json';

            if (!copy($this->dataFile, $backupFile)) {
                logWarning("SecureDataManager: Не удалось создать бэкап", [
                    'source' => $this->dataFile,
                    'backup_file' => $backupFile,
                    'backup_dir_exists' => is_dir($this->backupDir),
                    'backup_dir_writable' => is_writable($this->backupDir)
                ]);
                return false;
            }

            logWarning("SecureDataManager: Бэкап создан", ['backup_file' => $backupFile, 'size' => $fileSize]);
            return true;

        } catch (Exception $e) {
            logWarning("SecureDataManager: Исключение при создании бэкапа", ['error' => $e->getMessage()]);
            return false;
        }
    }
    
    /**
     * АТОМАРНАЯ ЗАПИСЬ ЧЕРЕЗ ВРЕМЕННЫЙ ФАЙЛ
     */
    private function atomicWrite(array $userData): bool {
        $tempFile = $this->dataFile . '.tmp.' . uniqid();
        
        try {
            // Кодируем данные
            $jsonData = json_encode($userData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
            if ($jsonData === false) {
                logError("SecureDataManager: Ошибка JSON кодирования", ['error' => json_last_error_msg()]);
                return false;
            }
            
            // Записываем во временный файл
            $bytesWritten = file_put_contents($tempFile, $jsonData, LOCK_EX);
            if ($bytesWritten === false) {
                logError("SecureDataManager: Не удалось записать во временный файл", ['temp_file' => $tempFile]);
                return false;
            }
            
            // Проверяем размер записанного файла
            if ($bytesWritten < $this->minFileSize) {
                logError("SecureDataManager: Записанный файл слишком мал", [
                    'bytes_written' => $bytesWritten,
                    'min_size' => $this->minFileSize
                ]);
                unlink($tempFile);
                return false;
            }
            
            // Атомарно перемещаем временный файл
            if (!rename($tempFile, $this->dataFile)) {
                logError("SecureDataManager: Не удалось переместить временный файл", [
                    'temp_file' => $tempFile,
                    'target_file' => $this->dataFile
                ]);
                unlink($tempFile);
                return false;
            }
            
            return true;
            
        } catch (Exception $e) {
            logError("SecureDataManager: Исключение при атомарной записи", [
                'error' => $e->getMessage(),
                'temp_file' => $tempFile
            ]);
            
            if (file_exists($tempFile)) {
                unlink($tempFile);
            }
            
            return false;
        }
    }
    
    /**
     * ВЕРИФИКАЦИЯ ЗАПИСАННЫХ ДАННЫХ
     */
    private function verifyWrittenData(array $originalData): bool {
        if (!file_exists($this->dataFile)) {
            logError("SecureDataManager: Файл не существует после записи");
            return false;
        }
        
        $fileSize = filesize($this->dataFile);
        if ($fileSize < $this->minFileSize) {
            logError("SecureDataManager: Записанный файл слишком мал", ['size' => $fileSize]);
            return false;
        }
        
        // Читаем и проверяем JSON
        $content = file_get_contents($this->dataFile);
        if ($content === false) {
            logError("SecureDataManager: Не удалось прочитать записанный файл");
            return false;
        }
        
        $decodedData = json_decode($content, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            logError("SecureDataManager: Записанный файл содержит невалидный JSON", [
                'error' => json_last_error_msg()
            ]);
            return false;
        }
        
        // Проверяем количество пользователей
        if (count($decodedData) !== count($originalData)) {
            logError("SecureDataManager: Количество пользователей не совпадает", [
                'original' => count($originalData),
                'written' => count($decodedData)
            ]);
            return false;
        }
        
        return true;
    }
    
    /**
     * ВОССТАНОВЛЕНИЕ ИЗ ПОСЛЕДНЕГО БЭКАПА (ЕСЛИ ЕСТЬ)
     */
    private function restoreFromBackup(): bool {
        try {
            // Ищем бэкапы для текущего файла
            $baseName = pathinfo($this->dataFile, PATHINFO_FILENAME);
            $backups = glob($this->backupDir . '/' . $baseName . '_backup_*.json');

            if (empty($backups)) {
                logWarning("SecureDataManager: Нет бэкапов для восстановления", [
                    'file' => $this->dataFile,
                    'backup_dir' => $this->backupDir,
                    'pattern' => $baseName . '_backup_*.json',
                    'backup_dir_exists' => is_dir($this->backupDir)
                ]);
                return false;
            }

            // Сортируем по времени создания (новые первыми)
            usort($backups, function($a, $b) {
                return filemtime($b) - filemtime($a);
            });

            $latestBackup = $backups[0];

            // Проверяем, что бэкап валидный
            if (!file_exists($latestBackup) || filesize($latestBackup) < $this->minFileSize) {
                logWarning("SecureDataManager: Последний бэкап невалидный", [
                    'backup_file' => $latestBackup,
                    'exists' => file_exists($latestBackup),
                    'size' => file_exists($latestBackup) ? filesize($latestBackup) : 0
                ]);
                return false;
            }

            if (!copy($latestBackup, $this->dataFile)) {
                logWarning("SecureDataManager: Не удалось восстановить из бэкапа", [
                    'backup_file' => $latestBackup,
                    'target_file' => $this->dataFile
                ]);
                return false;
            }

            logWarning("SecureDataManager: Данные восстановлены из бэкапа", [
                'backup_file' => $latestBackup,
                'backup_size' => filesize($latestBackup)
            ]);

            return true;

        } catch (Exception $e) {
            logWarning("SecureDataManager: Исключение при восстановлении из бэкапа", [
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }
    
    /**
     * ОЧИСТКА СТАРЫХ БЭКАПОВ
     */
    private function cleanupOldBackups(): void {
        // Ищем все бэкапы для текущего файла
        $baseName = pathinfo($this->dataFile, PATHINFO_FILENAME);
        $backups = glob($this->backupDir . '/' . $baseName . '_backup_*.json');

        if (count($backups) <= $this->maxBackups) {
            return;
        }

        // Сортируем по времени создания (старые первыми)
        usort($backups, function($a, $b) {
            return filemtime($a) - filemtime($b);
        });

        $toDelete = array_slice($backups, 0, count($backups) - $this->maxBackups);

        foreach ($toDelete as $backup) {
            if (unlink($backup)) {
                logWarning("SecureDataManager: Удален старый бэкап", ['file' => basename($backup)]);
            }
        }
    }
    
    /**
     * СЛИЯНИЕ ДАННЫХ ИЗ ДОПОЛНИТЕЛЬНОГО ФАЙЛА
     */
    public function mergeAdditionalData($additionalFile): bool {
        if (!file_exists($additionalFile)) {
            logError("SecureDataManager: Дополнительный файл не существует", ['file' => $additionalFile]);
            return false;
        }
        
        // Загружаем основные данные
        $mainData = $this->loadUserData();
        if ($mainData === false) {
            logError("SecureDataManager: Не удалось загрузить основные данные");
            return false;
        }
        
        // Загружаем дополнительные данные
        $additionalContent = file_get_contents($additionalFile);
        if ($additionalContent === false) {
            logError("SecureDataManager: Не удалось прочитать дополнительный файл");
            return false;
        }
        
        $additionalData = json_decode($additionalContent, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            logError("SecureDataManager: Дополнительный файл содержит невалидный JSON", [
                'error' => json_last_error_msg()
            ]);
            return false;
        }
        
        // Создаем бэкап перед слиянием
        $backupFile = $this->backupDir . '/user_data_before_merge_' . date('Y-m-d_H-i-s') . '.json';
        if (!copy($this->dataFile, $backupFile)) {
            logError("SecureDataManager: Не удалось создать бэкап перед слиянием");
            return false;
        }
        
        // Слияние данных
        $mergedCount = 0;
        $updatedCount = 0;
        
        foreach ($additionalData as $userId => $userData) {
            if (!isset($mainData[$userId])) {
                // Новый пользователь
                $mainData[$userId] = $userData;
                $mergedCount++;
            } else {
                // Обновляем существующего пользователя (только если данные новее)
                if (isset($userData['last_activity']) && isset($mainData[$userId]['last_activity'])) {
                    if (strtotime($userData['last_activity']) > strtotime($mainData[$userId]['last_activity'])) {
                        $mainData[$userId] = array_merge($mainData[$userId], $userData);
                        $updatedCount++;
                    }
                } else {
                    // Если нет информации о времени, объединяем данные
                    $mainData[$userId] = array_merge($mainData[$userId], $userData);
                    $updatedCount++;
                }
            }
        }
        
        // Сохраняем объединенные данные
        if ($this->saveUserDataSecure($mainData)) {
            logWarning("SecureDataManager: Данные успешно объединены", [
                'merged_new_users' => $mergedCount,
                'updated_users' => $updatedCount,
                'total_users' => count($mainData),
                'backup_file' => $backupFile
            ]);
            
            // Создаем бэкап дополнительного файла
            $additionalBackup = $additionalFile . '_merged_' . date('Y-m-d_H-i-s') . '.backup';
            copy($additionalFile, $additionalBackup);
            
            return true;
        } else {
            logError("SecureDataManager: Не удалось сохранить объединенные данные");
            return false;
        }
    }
    
    /**
     * БЕЗОПАСНАЯ ЗАГРУЗКА ДАННЫХ
     */
    public function loadUserData() {
        if (!file_exists($this->dataFile)) {
            logWarning("SecureDataManager: Файл данных не существует", ['file' => $this->dataFile]);
            return [];
        }
        
        $fileSize = filesize($this->dataFile);
        if ($fileSize < $this->minFileSize) {
            logWarning("SecureDataManager: Файл слишком мал, возможно поврежден", [
                'file' => $this->dataFile,
                'size' => $fileSize
            ]);

            // Пытаемся восстановить из бэкапа (если есть)
            if ($this->restoreFromBackup()) {
                logWarning("SecureDataManager: Файл восстановлен из бэкапа, повторная загрузка");
                return $this->loadUserData(); // Рекурсивно загружаем восстановленные данные
            }

            // Если бэкапов нет, возвращаем пустой массив (для первого запуска)
            logWarning("SecureDataManager: Нет бэкапов для восстановления, возвращаем пустой массив");
            return [];
        }
        
        $content = file_get_contents($this->dataFile);
        if ($content === false) {
            logError("SecureDataManager: Не удалось прочитать файл", ['file' => $this->dataFile]);
            return false;
        }
        
        $data = json_decode($content, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            logWarning("SecureDataManager: Файл содержит невалидный JSON", [
                'file' => $this->dataFile,
                'error' => json_last_error_msg()
            ]);

            // Пытаемся восстановить из бэкапа (если есть)
            if ($this->restoreFromBackup()) {
                logWarning("SecureDataManager: JSON восстановлен из бэкапа, повторная загрузка");
                return $this->loadUserData(); // Рекурсивно загружаем восстановленные данные
            }

            // Если бэкапов нет, возвращаем пустой массив
            logWarning("SecureDataManager: Нет бэкапов для восстановления JSON, возвращаем пустой массив");
            return [];
        }
        
        return $data;
    }
}
?>
