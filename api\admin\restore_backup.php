<?php
/**
 * Скрипт для восстановления данных из резервных копий
 * Позволяет восстановить данные, если что-то пошло не так при очистке
 */

// Подключаем аутентификацию
require_once __DIR__ . '/auth.php';
session_start();
if (!isAuthenticated()) {
    http_response_code(403);
    echo json_encode(['success' => false, 'error' => 'Unauthorized']);
    exit;
}

header('Content-Type: application/json');

function getAvailableBackups() {
    $database_dir = __DIR__ . '/../../database/';
    $backups = [];
    
    // Ищем файлы резервных копий
    $backup_files = glob($database_dir . '*.backup*');
    
    foreach ($backup_files as $backup_file) {
        $filename = basename($backup_file);
        $original_file = '';
        $backup_date = '';
        
        // Определяем оригинальный файл и дату
        if (preg_match('/^(.+)\.backup\.(.+)$/', $filename, $matches)) {
            $original_file = $matches[1];
            $backup_date = $matches[2];
        } elseif (preg_match('/^(.+)\.backup$/', $filename, $matches)) {
            $original_file = $matches[1];
            $backup_date = date('Y-m-d H:i:s', filemtime($backup_file));
        }
        
        if ($original_file) {
            $backups[] = [
                'backup_file' => $filename,
                'original_file' => $original_file,
                'backup_date' => $backup_date,
                'size' => filesize($backup_file),
                'size_mb' => round(filesize($backup_file) / 1024 / 1024, 2)
            ];
        }
    }
    
    // Сортируем по дате (новые сначала)
    usort($backups, function($a, $b) {
        return strcmp($b['backup_date'], $a['backup_date']);
    });
    
    return $backups;
}

function restoreFromBackup($backup_filename) {
    $database_dir = __DIR__ . '/../../database/';
    $backup_path = $database_dir . $backup_filename;
    
    if (!file_exists($backup_path)) {
        return ['success' => false, 'error' => 'Backup file not found'];
    }
    
    // Определяем оригинальный файл
    $original_file = '';
    if (preg_match('/^(.+)\.backup/', $backup_filename, $matches)) {
        $original_file = $matches[1];
    }
    
    if (!$original_file) {
        return ['success' => false, 'error' => 'Cannot determine original filename'];
    }
    
    $original_path = $database_dir . $original_file;
    
    // Создаем резервную копию текущего файла перед восстановлением
    if (file_exists($original_path)) {
        $current_backup = $original_path . '.backup.before_restore.' . date('Y-m-d_H-i-s');
        if (!copy($original_path, $current_backup)) {
            return ['success' => false, 'error' => 'Failed to backup current file'];
        }
    }
    
    // Восстанавливаем из резервной копии
    if (!copy($backup_path, $original_path)) {
        return ['success' => false, 'error' => 'Failed to restore from backup'];
    }
    
    return [
        'success' => true,
        'restored_file' => $original_file,
        'from_backup' => $backup_filename,
        'current_backup_created' => isset($current_backup) ? basename($current_backup) : null
    ];
}

try {
    $action = $_GET['action'] ?? 'list';
    
    if ($action === 'list') {
        // Показываем список доступных резервных копий
        $backups = getAvailableBackups();
        
        $response = [
            'success' => true,
            'backups' => $backups,
            'total_backups' => count($backups)
        ];
        
    } elseif ($action === 'restore') {
        // Восстанавливаем из резервной копии
        $backup_file = $_GET['backup_file'] ?? '';
        
        if (empty($backup_file)) {
            throw new Exception('Backup file parameter is required');
        }
        
        $response = restoreFromBackup($backup_file);
        
    } else {
        throw new Exception('Invalid action parameter');
    }
    
} catch (Exception $e) {
    $response = [
        'success' => false,
        'error' => $e->getMessage()
    ];
}

echo json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
