<?php
/**
 * ЗАЩИЩЕННЫЙ МЕНЕДЖЕР ДАННЫХ ПОЛЬЗОВАТЕЛЕЙ
 * Предотвращает потерю данных через атомарную запись и множественные backup'ы
 */

// config.php должен быть подключен до этого файла
if (!defined('USER_DATA_FILE')) {
    require_once __DIR__ . '/config.php';
}

class SecureUserDataManager {
    private static $instance = null;
    private $lockFile;
    private $backupDir;
    private $maxBackups = 20; // Увеличиваем до 20 backup'ов
    private $maxBackupAge = 604800; // 7 дней в секундах
    private $lockTimeout = 30; // 30 секунд
    private $lastIntegrityCheck = 0; // Время последней проверки целостности
    private $integrityCheckInterval = 3600; // Проверка раз в час
    
    private function __construct() {
        $this->lockFile = USER_DATA_FILE . '.lock';
        $this->backupDir = dirname(USER_DATA_FILE) . '/backups';
        
        // Создаем директорию для backup'ов
        if (!is_dir($this->backupDir)) {
            mkdir($this->backupDir, 0755, true);
        }
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * БЕЗОПАСНАЯ ЗАГРУЗКА ДАННЫХ
     */
    public function loadUserData() {
        // Проверяем основной файл
        if (!file_exists(USER_DATA_FILE)) {
            // Пытаемся восстановить из последнего backup'а
            $restored = $this->restoreFromBackup();
            if ($restored) {
                error_log("SECURE: Данные восстановлены из backup'а");
                return $this->loadUserData();
            }
            
            // Создаем пустой файл
            $this->createEmptyFile();
            return [];
        }
        
        $content = file_get_contents(USER_DATA_FILE);
        if ($content === false) {
            error_log("SECURE ERROR: Не удалось прочитать файл данных");
            return $this->restoreFromBackup() ? $this->loadUserData() : [];
        }
        
        if (empty(trim($content))) {
            error_log("SECURE WARNING: Файл данных пуст, восстанавливаем из backup'а");
            return $this->restoreFromBackup() ? $this->loadUserData() : [];
        }
        
        $data = json_decode($content, true);
        if ($data === null && json_last_error() !== JSON_ERROR_NONE) {
            error_log("SECURE ERROR: Поврежденный JSON, восстанавливаем из backup'а: " . json_last_error_msg());
            return $this->restoreFromBackup() ? $this->loadUserData() : [];
        }
        
        if (!is_array($data)) {
            error_log("SECURE ERROR: Данные не являются массивом, восстанавливаем из backup'а");
            return $this->restoreFromBackup() ? $this->loadUserData() : [];
        }

        // Периодическая проверка целостности (раз в час)
        $this->performPeriodicIntegrityCheck();

        return $data;
    }
    
    /**
     * АТОМАРНАЯ БЕЗОПАСНАЯ ЗАПИСЬ ДАННЫХ
     */
    public function saveUserData($userData) {
        if (!is_array($userData)) {
            error_log("SECURE ERROR: Данные должны быть массивом");
            return false;
        }
        
        // Получаем эксклюзивную блокировку
        $lockHandle = $this->acquireLock();
        if (!$lockHandle) {
            error_log("SECURE ERROR: Не удалось получить блокировку для записи");
            return false;
        }
        
        try {
            // 1. Создаем backup текущих данных
            $this->createBackup();
            
            // 2. Проверяем целостность данных
            if (!$this->validateUserData($userData)) {
                error_log("SECURE ERROR: Данные не прошли валидацию");
                return false;
            }
            
            // 3. Кодируем в JSON
            $jsonData = json_encode($userData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
            if ($jsonData === false) {
                error_log("SECURE ERROR: Ошибка кодирования JSON: " . json_last_error_msg());
                return false;
            }
            
            // 4. АТОМАРНАЯ ЗАПИСЬ через временный файл
            $tempFile = USER_DATA_FILE . '.tmp.' . uniqid();
            
            // Записываем во временный файл
            $bytesWritten = file_put_contents($tempFile, $jsonData, LOCK_EX);
            if ($bytesWritten === false) {
                error_log("SECURE ERROR: Не удалось записать во временный файл");
                @unlink($tempFile);
                return false;
            }
            
            // Проверяем целостность записанного файла
            $verifyContent = file_get_contents($tempFile);
            $verifyData = json_decode($verifyContent, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                error_log("SECURE ERROR: Временный файл поврежден: " . json_last_error_msg());
                @unlink($tempFile);
                return false;
            }
            
            // Атомарно перемещаем временный файл на место основного
            if (!rename($tempFile, USER_DATA_FILE)) {
                error_log("SECURE ERROR: Не удалось переместить временный файл");
                @unlink($tempFile);
                return false;
            }
            
            // Устанавливаем права доступа
            @chmod(USER_DATA_FILE, 0664);
            
            error_log("SECURE SUCCESS: Данные безопасно сохранены (" . strlen($jsonData) . " байт)");
            return true;
            
        } finally {
            // Освобождаем блокировку
            $this->releaseLock($lockHandle);
        }
    }
    
    /**
     * Создает backup текущих данных
     */
    private function createBackup() {
        if (!file_exists(USER_DATA_FILE)) {
            return false;
        }
        
        $timestamp = date('Y-m-d_H-i-s');
        $backupFile = $this->backupDir . "/user_data_backup_{$timestamp}.json";
        
        if (copy(USER_DATA_FILE, $backupFile)) {
            error_log("SECURE: Создан backup: " . basename($backupFile));
            $this->cleanOldBackups();
            return true;
        }
        
        return false;
    }
    
    /**
     * Восстанавливает данные из последнего backup'а
     */
    private function restoreFromBackup() {
        $backups = glob($this->backupDir . '/user_data_backup_*.json');
        if (empty($backups)) {
            return false;
        }
        
        // Сортируем по времени создания (новые первыми)
        usort($backups, function($a, $b) {
            return filemtime($b) - filemtime($a);
        });
        
        foreach ($backups as $backup) {
            $content = file_get_contents($backup);
            if ($content !== false) {
                $data = json_decode($content, true);
                if (json_last_error() === JSON_ERROR_NONE && is_array($data)) {
                    if (copy($backup, USER_DATA_FILE)) {
                        error_log("SECURE: Данные восстановлены из backup: " . basename($backup));
                        return true;
                    }
                }
            }
        }
        
        return false;
    }
    
    /**
     * Валидация данных пользователей
     */
    private function validateUserData($userData) {
        if (!is_array($userData)) {
            return false;
        }
        
        foreach ($userData as $userId => $userInfo) {
            if (!is_numeric($userId)) {
                error_log("SECURE ERROR: Некорректный ID пользователя: $userId");
                return false;
            }
            
            if (!is_array($userInfo)) {
                error_log("SECURE ERROR: Данные пользователя $userId не являются массивом");
                return false;
            }
            
            // Проверяем обязательные поля
            if (!isset($userInfo['balance']) || !is_numeric($userInfo['balance'])) {
                error_log("SECURE ERROR: Некорректный баланс у пользователя $userId");
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * Получает эксклюзивную блокировку
     */
    private function acquireLock() {
        $startTime = time();
        
        while (time() - $startTime < $this->lockTimeout) {
            $lockHandle = fopen($this->lockFile, 'w');
            if ($lockHandle && flock($lockHandle, LOCK_EX | LOCK_NB)) {
                return $lockHandle;
            }
            
            if ($lockHandle) {
                fclose($lockHandle);
            }
            
            usleep(100000); // 100ms
        }
        
        return false;
    }
    
    /**
     * Освобождает блокировку
     */
    private function releaseLock($lockHandle) {
        if ($lockHandle) {
            flock($lockHandle, LOCK_UN);
            fclose($lockHandle);
            @unlink($this->lockFile);
        }
    }
    
    /**
     * Создает пустой файл данных
     */
    private function createEmptyFile() {
        file_put_contents(USER_DATA_FILE, '{}', LOCK_EX);
        @chmod(USER_DATA_FILE, 0664);
    }
    
    /**
     * Удаляет старые backup'ы по количеству и возрасту
     */
    private function cleanOldBackups() {
        $backups = glob($this->backupDir . '/user_data_backup_*.json');
        if (empty($backups)) {
            return;
        }

        $currentTime = time();
        $deletedCount = 0;

        // Сортируем по времени (старые первыми)
        usort($backups, function($a, $b) {
            return filemtime($a) - filemtime($b);
        });

        // 1. Удаляем backup'ы старше maxBackupAge (7 дней)
        foreach ($backups as $key => $backup) {
            $fileAge = $currentTime - filemtime($backup);
            if ($fileAge > $this->maxBackupAge) {
                if (@unlink($backup)) {
                    $deletedCount++;
                    error_log("SECURE: Удален старый backup (возраст " . round($fileAge/86400, 1) . " дней): " . basename($backup));
                    unset($backups[$key]);
                }
            }
        }

        // 2. Удаляем лишние backup'ы если их больше maxBackups
        $backups = array_values($backups); // Переиндексируем массив
        if (count($backups) > $this->maxBackups) {
            $toDelete = array_slice($backups, 0, count($backups) - $this->maxBackups);
            foreach ($toDelete as $backup) {
                if (@unlink($backup)) {
                    $deletedCount++;
                    error_log("SECURE: Удален лишний backup: " . basename($backup));
                }
            }
        }

        if ($deletedCount > 0) {
            error_log("SECURE: Автоочистка завершена. Удалено backup'ов: $deletedCount");
        }
    }

    /**
     * Принудительная очистка всех backup'ов старше указанного времени
     * @param int $maxAge Максимальный возраст в секундах (по умолчанию 24 часа)
     * @return int Количество удаленных файлов
     */
    public function forceCleanBackups($maxAge = 86400) {
        $backups = glob($this->backupDir . '/user_data_backup_*.json');
        if (empty($backups)) {
            return 0;
        }

        $currentTime = time();
        $deletedCount = 0;

        foreach ($backups as $backup) {
            $fileAge = $currentTime - filemtime($backup);
            if ($fileAge > $maxAge) {
                if (@unlink($backup)) {
                    $deletedCount++;
                    error_log("SECURE: Принудительно удален backup (возраст " . round($fileAge/3600, 1) . " часов): " . basename($backup));
                }
            }
        }

        error_log("SECURE: Принудительная очистка завершена. Удалено backup'ов: $deletedCount");
        return $deletedCount;
    }

    /**
     * Получает информацию о backup'ах
     * @return array Информация о backup'ах
     */
    public function getBackupInfo() {
        $backups = glob($this->backupDir . '/user_data_backup_*.json');
        if (empty($backups)) {
            return [
                'count' => 0,
                'total_size' => 0,
                'oldest' => null,
                'newest' => null
            ];
        }

        $totalSize = 0;
        $times = [];

        foreach ($backups as $backup) {
            $totalSize += filesize($backup);
            $times[] = filemtime($backup);
        }

        return [
            'count' => count($backups),
            'total_size' => $totalSize,
            'total_size_mb' => round($totalSize / 1024 / 1024, 2),
            'oldest' => date('Y-m-d H:i:s', min($times)),
            'newest' => date('Y-m-d H:i:s', max($times)),
            'max_allowed' => $this->maxBackups,
            'max_age_days' => round($this->maxBackupAge / 86400, 1)
        ];
    }

    /**
     * Периодическая проверка целостности данных (без cron)
     */
    private function performPeriodicIntegrityCheck() {
        $currentTime = time();

        // Проверяем, нужна ли проверка целостности
        if ($currentTime - $this->lastIntegrityCheck < $this->integrityCheckInterval) {
            return; // Еще рано для проверки
        }

        $this->lastIntegrityCheck = $currentTime;

        try {
            // Простая проверка целостности
            $userCount = count($this->loadUserData());
            $fileSize = file_exists(USER_DATA_FILE) ? filesize(USER_DATA_FILE) : 0;

            // Логируем статистику
            error_log("SECURE: Периодическая проверка - пользователей: $userCount, размер файла: " . number_format($fileSize) . " байт");

            // Проверяем критичные проблемы
            if ($userCount < 100) {
                error_log("SECURE WARNING: Подозрительно мало пользователей: $userCount");
            }

            if ($fileSize < 10000) {
                error_log("SECURE WARNING: Подозрительно маленький размер файла: $fileSize байт");
            }

            // Принудительная очистка backup'ов раз в день
            if ($currentTime % 86400 < $this->integrityCheckInterval) {
                $this->cleanOldBackups();
            }

        } catch (Exception $e) {
            error_log("SECURE ERROR: Ошибка периодической проверки: " . $e->getMessage());
        }
    }
}

// Глобальные функции для совместимости
function loadUserData() {
    return SecureUserDataManager::getInstance()->loadUserData();
}

function saveUserData($userData) {
    return SecureUserDataManager::getInstance()->saveUserData($userData);
}
?>
