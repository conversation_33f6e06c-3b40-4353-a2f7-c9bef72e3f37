<?php
/**
 * МОНИТОР ЦЕЛОСТНОСТИ ДАННЫХ ПОЛЬЗОВАТЕЛЕЙ
 * Проверяет целостность файла user_data.json и предотвращает потерю данных
 */

require_once __DIR__ . '/secure_user_data_manager.php';

class DataIntegrityMonitor {
    private $alertFile;
    private $statsFile;
    private $minExpectedUsers = 300; // Минимальное ожидаемое количество пользователей
    
    public function __construct() {
        $this->alertFile = __DIR__ . '/data_integrity_alerts.log';
        $this->statsFile = __DIR__ . '/data_stats.json';
    }
    
    /**
     * Выполняет полную проверку целостности данных
     */
    public function performIntegrityCheck() {
        $results = [
            'timestamp' => time(),
            'checks' => [],
            'alerts' => [],
            'status' => 'OK'
        ];
        
        // 1. Проверка существования файла
        $results['checks']['file_exists'] = $this->checkFileExists();
        
        // 2. Проверка размера файла
        $results['checks']['file_size'] = $this->checkFileSize();
        
        // 3. Проверка JSON структуры
        $results['checks']['json_valid'] = $this->checkJsonStructure();
        
        // 4. Проверка количества пользователей
        $results['checks']['user_count'] = $this->checkUserCount();
        
        // 5. Проверка структуры данных пользователей
        $results['checks']['data_structure'] = $this->checkDataStructure();
        
        // 6. Проверка на подозрительные изменения
        $results['checks']['suspicious_changes'] = $this->checkSuspiciousChanges();
        
        // Определяем общий статус
        foreach ($results['checks'] as $check) {
            if ($check['status'] === 'ERROR') {
                $results['status'] = 'ERROR';
                break;
            } elseif ($check['status'] === 'WARNING' && $results['status'] === 'OK') {
                $results['status'] = 'WARNING';
            }
        }
        
        // Сохраняем результаты
        $this->saveResults($results);
        
        // Отправляем алерты если нужно
        if ($results['status'] !== 'OK') {
            $this->sendAlert($results);
        }
        
        return $results;
    }
    
    private function checkFileExists() {
        $exists = file_exists(USER_DATA_FILE);
        return [
            'name' => 'File Existence',
            'status' => $exists ? 'OK' : 'ERROR',
            'message' => $exists ? 'File exists' : 'USER DATA FILE MISSING!',
            'details' => ['file' => USER_DATA_FILE]
        ];
    }
    
    private function checkFileSize() {
        if (!file_exists(USER_DATA_FILE)) {
            return [
                'name' => 'File Size',
                'status' => 'ERROR',
                'message' => 'File does not exist',
                'details' => []
            ];
        }
        
        $size = filesize(USER_DATA_FILE);
        $minSize = 1000; // Минимум 1KB для файла с пользователями
        
        $status = 'OK';
        $message = "File size: " . number_format($size) . " bytes";
        
        if ($size < $minSize) {
            $status = 'ERROR';
            $message = "File too small: $size bytes (expected > $minSize)";
        } elseif ($size < 10000) {
            $status = 'WARNING';
            $message = "File smaller than expected: $size bytes";
        }
        
        return [
            'name' => 'File Size',
            'status' => $status,
            'message' => $message,
            'details' => ['size' => $size, 'min_expected' => $minSize]
        ];
    }
    
    private function checkJsonStructure() {
        if (!file_exists(USER_DATA_FILE)) {
            return [
                'name' => 'JSON Structure',
                'status' => 'ERROR',
                'message' => 'File does not exist',
                'details' => []
            ];
        }
        
        $content = file_get_contents(USER_DATA_FILE);
        if ($content === false) {
            return [
                'name' => 'JSON Structure',
                'status' => 'ERROR',
                'message' => 'Cannot read file',
                'details' => []
            ];
        }
        
        if (empty(trim($content))) {
            return [
                'name' => 'JSON Structure',
                'status' => 'ERROR',
                'message' => 'File is empty',
                'details' => []
            ];
        }
        
        $data = json_decode($content, true);
        $jsonError = json_last_error();
        
        if ($jsonError !== JSON_ERROR_NONE) {
            return [
                'name' => 'JSON Structure',
                'status' => 'ERROR',
                'message' => 'Invalid JSON: ' . json_last_error_msg(),
                'details' => ['json_error' => $jsonError]
            ];
        }
        
        if (!is_array($data)) {
            return [
                'name' => 'JSON Structure',
                'status' => 'ERROR',
                'message' => 'Root element is not an array',
                'details' => ['type' => gettype($data)]
            ];
        }
        
        return [
            'name' => 'JSON Structure',
            'status' => 'OK',
            'message' => 'Valid JSON structure',
            'details' => ['users_count' => count($data)]
        ];
    }
    
    private function checkUserCount() {
        $data = SecureUserDataManager::getInstance()->loadUserData();
        $count = count($data);
        
        $status = 'OK';
        $message = "Users count: $count";
        
        if ($count < $this->minExpectedUsers) {
            $status = 'ERROR';
            $message = "Too few users: $count (expected > {$this->minExpectedUsers})";
        } elseif ($count < $this->minExpectedUsers * 1.1) {
            $status = 'WARNING';
            $message = "User count lower than expected: $count";
        }
        
        return [
            'name' => 'User Count',
            'status' => $status,
            'message' => $message,
            'details' => ['count' => $count, 'min_expected' => $this->minExpectedUsers]
        ];
    }
    
    private function checkDataStructure() {
        $data = SecureUserDataManager::getInstance()->loadUserData();
        $errors = [];
        $warnings = [];
        $checkedUsers = 0;
        
        foreach ($data as $userId => $userInfo) {
            $checkedUsers++;
            
            // Проверяем ID пользователя
            if (!is_numeric($userId)) {
                $errors[] = "Invalid user ID: $userId";
            }
            
            // Проверяем структуру данных пользователя
            if (!is_array($userInfo)) {
                $errors[] = "User $userId data is not an array";
                continue;
            }
            
            // Проверяем обязательные поля
            if (!isset($userInfo['balance']) || !is_numeric($userInfo['balance'])) {
                $errors[] = "User $userId has invalid balance";
            }
            
            // Ограничиваем количество проверяемых пользователей для производительности
            if ($checkedUsers >= 100) break;
        }
        
        $status = 'OK';
        $message = "Data structure is valid";
        
        if (!empty($errors)) {
            $status = 'ERROR';
            $message = "Data structure errors: " . implode(', ', array_slice($errors, 0, 3));
        } elseif (!empty($warnings)) {
            $status = 'WARNING';
            $message = "Data structure warnings: " . implode(', ', array_slice($warnings, 0, 3));
        }
        
        return [
            'name' => 'Data Structure',
            'status' => $status,
            'message' => $message,
            'details' => [
                'checked_users' => $checkedUsers,
                'errors' => $errors,
                'warnings' => $warnings
            ]
        ];
    }
    
    private function checkSuspiciousChanges() {
        $currentStats = $this->getCurrentStats();
        $previousStats = $this->loadPreviousStats();
        
        if (!$previousStats) {
            return [
                'name' => 'Suspicious Changes',
                'status' => 'OK',
                'message' => 'No previous stats for comparison',
                'details' => $currentStats
            ];
        }
        
        $alerts = [];
        
        // Проверяем резкое уменьшение количества пользователей
        $userDiff = $currentStats['user_count'] - $previousStats['user_count'];
        if ($userDiff < -10) {
            $alerts[] = "User count decreased by " . abs($userDiff);
        }
        
        // Проверяем резкое уменьшение размера файла
        $sizeDiff = $currentStats['file_size'] - $previousStats['file_size'];
        if ($sizeDiff < -50000) { // Уменьшение более чем на 50KB
            $alerts[] = "File size decreased by " . number_format(abs($sizeDiff)) . " bytes";
        }
        
        $status = empty($alerts) ? 'OK' : 'WARNING';
        $message = empty($alerts) ? 'No suspicious changes detected' : implode(', ', $alerts);
        
        return [
            'name' => 'Suspicious Changes',
            'status' => $status,
            'message' => $message,
            'details' => [
                'current' => $currentStats,
                'previous' => $previousStats,
                'changes' => [
                    'users' => $userDiff,
                    'file_size' => $sizeDiff
                ]
            ]
        ];
    }
    
    private function getCurrentStats() {
        $data = SecureUserDataManager::getInstance()->loadUserData();
        return [
            'timestamp' => time(),
            'user_count' => count($data),
            'file_size' => file_exists(USER_DATA_FILE) ? filesize(USER_DATA_FILE) : 0,
            'file_mtime' => file_exists(USER_DATA_FILE) ? filemtime(USER_DATA_FILE) : 0
        ];
    }
    
    private function loadPreviousStats() {
        if (!file_exists($this->statsFile)) {
            return null;
        }
        
        $content = file_get_contents($this->statsFile);
        return $content ? json_decode($content, true) : null;
    }
    
    private function saveResults($results) {
        // Сохраняем текущую статистику
        $currentStats = $this->getCurrentStats();
        file_put_contents($this->statsFile, json_encode($currentStats, JSON_PRETTY_PRINT), LOCK_EX);
        
        // Логируем результаты
        $logMessage = "[" . date('Y-m-d H:i:s') . "] Integrity Check: " . $results['status'];
        if ($results['status'] !== 'OK') {
            $logMessage .= " - Issues found";
        }
        error_log($logMessage);
    }
    
    private function sendAlert($results) {
        $alertMessage = "DATA INTEGRITY ALERT: " . $results['status'] . "\n";
        $alertMessage .= "Timestamp: " . date('Y-m-d H:i:s', $results['timestamp']) . "\n";
        
        foreach ($results['checks'] as $check) {
            if ($check['status'] !== 'OK') {
                $alertMessage .= "- {$check['name']}: {$check['message']}\n";
            }
        }
        
        // Записываем в файл алертов
        file_put_contents($this->alertFile, $alertMessage . "\n", FILE_APPEND | LOCK_EX);
        
        // Логируем критичные алерты
        error_log("CRITICAL: " . $alertMessage);
    }
}

// Если скрипт запущен напрямую
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    $monitor = new DataIntegrityMonitor();
    $results = $monitor->performIntegrityCheck();
    
    echo "Data Integrity Check Results:\n";
    echo "Status: " . $results['status'] . "\n";
    echo "Timestamp: " . date('Y-m-d H:i:s', $results['timestamp']) . "\n\n";
    
    foreach ($results['checks'] as $check) {
        echo "✓ {$check['name']}: {$check['status']} - {$check['message']}\n";
    }
}

?>
