<?php
/**
 * МЕНЕДЖЕР BACKUP'ОВ
 * Управление backup'ами данных пользователей
 */

require_once __DIR__ . '/secure_user_data_manager.php';

class BackupManager {
    private $secureManager;
    
    public function __construct() {
        $this->secureManager = SecureUserDataManager::getInstance();
    }
    
    /**
     * Показывает информацию о backup'ах
     */
    public function showInfo() {
        $info = $this->secureManager->getBackupInfo();
        
        echo "📦 ИНФОРМАЦИЯ О BACKUP'АХ\n";
        echo "========================\n";
        echo "Количество backup'ов: {$info['count']}\n";
        echo "Общий размер: {$info['total_size_mb']} MB\n";
        echo "Максимально разрешено: {$info['max_allowed']}\n";
        echo "Максимальный возраст: {$info['max_age_days']} дней\n";
        
        if ($info['count'] > 0) {
            echo "Самый старый: {$info['oldest']}\n";
            echo "Самый новый: {$info['newest']}\n";
        }
        
        return $info;
    }
    
    /**
     * Очищает старые backup'ы
     */
    public function cleanOld($maxAge = 86400) {
        echo "🧹 ОЧИСТКА СТАРЫХ BACKUP'ОВ\n";
        echo "===========================\n";
        
        $deleted = $this->secureManager->forceCleanBackups($maxAge);
        
        if ($deleted > 0) {
            echo "✅ Удалено backup'ов: $deleted\n";
        } else {
            echo "ℹ️  Нет backup'ов для удаления\n";
        }
        
        return $deleted;
    }
    
    /**
     * Создает новый backup
     */
    public function createBackup() {
        echo "💾 СОЗДАНИЕ НОВОГО BACKUP'А\n";
        echo "===========================\n";
        
        // Загружаем данные и сохраняем (это создаст backup)
        $userData = $this->secureManager->loadUserData();
        $result = $this->secureManager->saveUserData($userData);
        
        if ($result) {
            echo "✅ Backup успешно создан\n";
        } else {
            echo "❌ Ошибка создания backup'а\n";
        }
        
        return $result;
    }
}

// Если скрипт запущен напрямую
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    $manager = new BackupManager();
    
    $command = $argv[1] ?? 'info';
    
    switch ($command) {
        case 'info':
            $manager->showInfo();
            break;
            
        case 'clean':
            $maxAge = isset($argv[2]) ? (int)$argv[2] : 86400; // 24 часа по умолчанию
            $manager->cleanOld($maxAge);
            $manager->showInfo();
            break;
            
        case 'clean-old':
            $manager->cleanOld(604800); // 7 дней
            $manager->showInfo();
            break;
            
        case 'create':
            $manager->createBackup();
            $manager->showInfo();
            break;
            
        default:
            echo "Использование:\n";
            echo "  php backup_manager.php info          - Показать информацию о backup'ах\n";
            echo "  php backup_manager.php clean [age]   - Очистить backup'ы старше age секунд\n";
            echo "  php backup_manager.php clean-old     - Очистить backup'ы старше 7 дней\n";
            echo "  php backup_manager.php create        - Создать новый backup\n";
            break;
    }
}

?>
