<?php
/**
 * api/admin/user_detail.php
 * Детальная страница пользователя
 */

// Включаем логирование
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/../error.log');
error_reporting(E_ALL);

// Подключаем файл с функциями аутентификации и авторизации
require_once __DIR__ . '/auth.php';

// Проверяем аутентификацию
session_start();
if (!isAuthenticated()) {
    header('Location: login.php');
    exit;
}

// --- Подключение зависимостей с проверкой ---
if (!(@require_once __DIR__ . '/../config.php')) {
    http_response_code(500);
    error_log('FATAL: config.php not found in admin/user_detail.php');
    die('Ошибка: Не удалось загрузить config.php');
}
if (!(@require_once __DIR__ . '/../db_mock.php')) {
    http_response_code(500);
    error_log('FATAL: db_mock.php not found in admin/user_detail.php');
    die('Ошибка: Не удалось загрузить db_mock.php');
}
if (!(@require_once __DIR__ . '/../security.php')) {
    http_response_code(500);
    error_log('FATAL: security.php not found in admin/user_detail.php');
    die('Ошибка: Не удалось загрузить security.php');
}

// Получаем ID пользователя из параметров
$userId = isset($_GET['id']) ? intval($_GET['id']) : 0;

if ($userId <= 0) {
    header('Location: users.php?error=' . urlencode('Неверный ID пользователя'));
    exit;
}

// Загрузка данных пользователей
$userData = loadUserData();
if (!is_array($userData)) {
    die('Ошибка: Не удалось загрузить данные пользователей');
}

// Проверяем существование пользователя
if (!isset($userData[$userId])) {
    header('Location: users.php?error=' . urlencode('Пользователь не найден'));
    exit;
}

$user = $userData[$userId];

// Функция для получения выплат пользователя
function getUserWithdrawals($userId) {
    $withdrawals = [];
    
    // Ищем выплаты в данных пользователя
    if (isset($GLOBALS['userData'][$userId]['withdrawals']) && is_array($GLOBALS['userData'][$userId]['withdrawals'])) {
        $withdrawals = $GLOBALS['userData'][$userId]['withdrawals'];
    }
    
    // Сортируем по дате создания (новые сначала)
    usort($withdrawals, function($a, $b) {
        $timeA = strtotime($a['created_at'] ?? '1970-01-01');
        $timeB = strtotime($b['created_at'] ?? '1970-01-01');
        return $timeB - $timeA;
    });
    
    return $withdrawals;
}

// Функция для получения истории аудита пользователя
function getUserAuditHistory($userId) {
    $auditFile = __DIR__ . '/../audit.log';
    $history = [];
    
    if (file_exists($auditFile)) {
        $lines = file($auditFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
        
        foreach ($lines as $line) {
            $data = json_decode($line, true);
            if ($data && isset($data['user_id']) && $data['user_id'] == $userId) {
                $history[] = $data;
            }
        }
    }
    
    // Сортируем по времени (новые сначала)
    usort($history, function($a, $b) {
        return strtotime($b['timestamp']) - strtotime($a['timestamp']);
    });
    
    return array_slice($history, 0, 50); // Последние 50 записей
}

function getReferralDetails($userId, $allUsers) {
    $referrals = [];
    if (isset($allUsers[$userId]['referrals']) && is_array($allUsers[$userId]['referrals'])) {
        foreach ($allUsers[$userId]['referrals'] as $referralId) {
            if (isset($allUsers[$referralId])) {
                $referralData = $allUsers[$referralId];
                $referrals[] = [
                    'id' => $referralId,
                    'username' => $referralData['username'] ?? 'N/A',
                    'total_earned' => $referralData['total_earned'] ?? 0,
                    'withdrawals_count' => count($referralData['withdrawals'] ?? []),
                ];
            }
        }
    }
    return $referrals;
}

// Получаем данные для отображения
$withdrawals = getUserWithdrawals($userId);
$auditHistory = getUserAuditHistory($userId);
$referralDetails = getReferralDetails($userId, $userData);

// Вычисляем статистику
$stats = [
    'total_withdrawals' => count($withdrawals),
    'successful_withdrawals' => 0,
    'failed_withdrawals' => 0,
    'pending_withdrawals' => 0,
    'total_withdrawn' => 0,
    'total_refunded' => 0,
    'referrals_count' => $user['referrals_count'] ?? 0,
    'referral_earnings' => $user['referral_earnings'] ?? 0,
    'total_earned' => $user['total_earned'] ?? 0,
    'suspicious_activity' => $user['suspicious_activity'] ?? 0
];

foreach ($withdrawals as $withdrawal) {
    $status = strtolower($withdrawal['status'] ?? 'unknown');
    $amount = $withdrawal['coins_amount'] ?? 0;
    
    if (in_array($status, ['finished', 'confirmed', 'completed'])) {
        $stats['successful_withdrawals']++;
        $stats['total_withdrawn'] += $amount;
    } elseif (in_array($status, ['failed', 'rejected', 'cancelled', 'expired'])) {
        $stats['failed_withdrawals']++;
        if (isset($withdrawal['refunded']) && $withdrawal['refunded']) {
            $stats['total_refunded'] += $withdrawal['refund_amount'] ?? $amount;
        }
    } else {
        $stats['pending_withdrawals']++;
    }
}

// Подключаем шаблон заголовка
include 'templates/header.php';
?>

<style>
    .user-activity-chart {
        height: 400px;
        min-height: 350px;
    }

    .user-activity-chart canvas {
        min-height: 350px !important;
        height: 400px !important;
    }
</style>

<div class="container-fluid">
    <div class="row">
        <?php include 'templates/sidebar.php'; ?>

        <!-- Основное содержимое -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">
                    <a href="users.php" class="text-decoration-none text-muted">
                        <i class="bi bi-arrow-left"></i>
                    </a>
                    Пользователь #<?php echo $userId; ?>
                    <?php
                    // Формируем отображаемое имя пользователя
                    $displayName = '';
                    if (!empty($user['username'])) {
                        $displayName .= ' (@' . $user['username'] . ')';
                    }
                    if (!empty($user['first_name']) || !empty($user['last_name'])) {
                        $fullName = trim(($user['first_name'] ?? '') . ' ' . ($user['last_name'] ?? ''));
                        if (!empty($displayName)) {
                            $displayName .= ' - ' . $fullName;
                        } else {
                            $displayName = ' - ' . $fullName;
                        }
                    }
                    echo htmlspecialchars($displayName);
                    ?>
                </h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#editUserModal">
                            <i class="bi bi-pencil"></i> Редактировать
                        </button>
                        <a href="users.php" class="btn btn-sm btn-outline-secondary">
                            <i class="bi bi-arrow-left"></i> Назад к списку
                        </a>
                    </div>
                </div>
            </div>

            <!-- Карточки со статистикой -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-primary"><?php echo $user['balance'] ?? 0; ?></h5>
                            <p class="card-text">Текущий баланс</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-success"><?php echo $stats['total_earned']; ?></h5>
                            <p class="card-text">Всего заработано</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-info"><?php echo $stats['referrals_count']; ?></h5>
                            <p class="card-text">Рефералов</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-warning"><?php echo $stats['suspicious_activity']; ?></h5>
                            <p class="card-text">Подозрительность</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Основная информация -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="bi bi-person"></i> Основная информация</h5>
                        </div>
                        <div class="card-body">
                            <table class="table table-sm">
                                <tr>
                                    <td><strong>ID:</strong></td>
                                    <td><?php echo $userId; ?></td>
                                </tr>
                                <?php if (!empty($user['username'])): ?>
                                <tr>
                                    <td><strong>Username:</strong></td>
                                    <td>@<?php echo htmlspecialchars($user['username']); ?></td>
                                </tr>
                                <?php endif; ?>
                                <?php if (!empty($user['first_name']) || !empty($user['last_name'])): ?>
                                <tr>
                                    <td><strong>Имя:</strong></td>
                                    <td><?php echo htmlspecialchars(trim(($user['first_name'] ?? '') . ' ' . ($user['last_name'] ?? ''))); ?></td>
                                </tr>
                                <?php endif; ?>
                                <tr>
                                    <td><strong>Дата регистрации:</strong></td>
                                    <td><?php echo isset($user['joined']) ? date('Y-m-d H:i:s', $user['joined']) : 'Неизвестно'; ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Статус:</strong></td>
                                    <td>
                                        <?php if (isset($user['blocked']) && $user['blocked']): ?>
                                            <span class="badge bg-danger">Заблокирован</span>
                                        <?php else: ?>
                                            <span class="badge bg-success">Активен</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="bi bi-graph-up"></i> Статистика выплат</h5>
                        </div>
                        <div class="card-body">
                            <table class="table table-sm">
                                <tr>
                                    <td><strong>Всего выплат:</strong></td>
                                    <td><?php echo $stats['total_withdrawals']; ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Успешных:</strong></td>
                                    <td><span class="text-success"><?php echo $stats['successful_withdrawals']; ?></span></td>
                                </tr>
                                <tr>
                                    <td><strong>Неудачных:</strong></td>
                                    <td><span class="text-danger"><?php echo $stats['failed_withdrawals']; ?></span></td>
                                </tr>
                                <tr>
                                    <td><strong>В обработке:</strong></td>
                                    <td><span class="text-warning"><?php echo $stats['pending_withdrawals']; ?></span></td>
                                </tr>
                                <tr>
                                    <td><strong>Выведено монет:</strong></td>
                                    <td><?php echo $stats['total_withdrawn']; ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Возвращено монет:</strong></td>
                                    <td><?php echo $stats['total_refunded']; ?></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Выплаты пользователя -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-credit-card"></i> История выплат</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($withdrawals)): ?>
                        <p class="text-muted">У пользователя нет выплат</p>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-sm table-striped">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Дата</th>
                                        <th>Сумма</th>
                                        <th>Валюта</th>
                                        <th>Адрес</th>
                                        <th>Статус</th>
                                        <th>Возврат</th>
                                        <th>Ошибка</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($withdrawals as $withdrawal): ?>
                                        <tr>
                                            <td>
                                                <small><?php echo htmlspecialchars($withdrawal['id'] ?? $withdrawal['payout_id'] ?? 'N/A'); ?></small>
                                            </td>
                                            <td>
                                                <small><?php echo isset($withdrawal['created_at']) ? date('Y-m-d H:i', strtotime($withdrawal['created_at'])) : 'N/A'; ?></small>
                                            </td>
                                            <td>
                                                <strong><?php echo $withdrawal['coins_amount'] ?? 0; ?></strong>
                                                <small class="text-muted">монет</small>
                                                <br>
                                                <small><?php echo $withdrawal['amount'] ?? 0; ?> <?php echo strtoupper($withdrawal['currency'] ?? ''); ?></small>
                                            </td>
                                            <td>
                                                <span class="badge bg-secondary"><?php echo strtoupper($withdrawal['currency'] ?? 'N/A'); ?></span>
                                            </td>
                                            <td>
                                                <small class="font-monospace">
                                                    <?php
                                                    $address = $withdrawal['address'] ?? 'N/A';
                                                    echo htmlspecialchars(strlen($address) > 20 ? substr($address, 0, 10) . '...' . substr($address, -10) : $address);
                                                    ?>
                                                </small>
                                            </td>
                                            <td>
                                                <?php
                                                $status = strtolower($withdrawal['status'] ?? 'unknown');
                                                $badgeClass = 'secondary';
                                                if (in_array($status, ['finished', 'confirmed', 'completed'])) {
                                                    $badgeClass = 'success';
                                                } elseif (in_array($status, ['failed', 'rejected', 'cancelled', 'expired'])) {
                                                    $badgeClass = 'danger';
                                                } elseif (in_array($status, ['waiting', 'confirming', 'sending'])) {
                                                    $badgeClass = 'warning';
                                                }
                                                ?>
                                                <span class="badge bg-<?php echo $badgeClass; ?>"><?php echo htmlspecialchars($status); ?></span>
                                            </td>
                                            <td>
                                                <?php if (isset($withdrawal['refunded']) && $withdrawal['refunded']): ?>
                                                    <span class="badge bg-info">
                                                        <?php echo $withdrawal['refund_amount'] ?? $withdrawal['coins_amount'] ?? 0; ?> монет
                                                    </span>
                                                    <br>
                                                    <small class="text-muted">
                                                        <?php echo isset($withdrawal['refund_date']) ? date('Y-m-d H:i', strtotime($withdrawal['refund_date'])) : ''; ?>
                                                    </small>
                                                <?php else: ?>
                                                    <span class="text-muted">-</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if (!empty($withdrawal['error_message'])): ?>
                                                    <small class="text-danger" title="<?php echo htmlspecialchars($withdrawal['error_message']); ?>">
                                                        <?php echo htmlspecialchars(strlen($withdrawal['error_message']) > 30 ? substr($withdrawal['error_message'], 0, 30) . '...' : $withdrawal['error_message']); ?>
                                                    </small>
                                                <?php else: ?>
                                                    <span class="text-muted">-</span>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="bi bi-graph-up"></i> Статистика активности</h5>
                    <div class="d-flex align-items-center">
                        <input type="date" id="dateFrom" class="form-control form-control-sm me-2" style="width: auto;">
                        <input type="date" id="dateTo" class="form-control form-control-sm me-2" style="width: auto;">
                        <button class="btn btn-sm btn-primary me-3" id="applyDateFilter">Применить</button>
                        <div class="btn-group btn-group-sm" role="group">
                            <button type="button" class="btn btn-outline-primary active" id="dailyBtn">По дням</button>
                            <button type="button" class="btn btn-outline-primary" id="hourlyBtn">По часам</button>
                        </div>
                    </div>
                </div>
                <div class="card-body user-activity-chart">
                    <canvas id="activityChart"></canvas>
                </div>
            </div>

            <!-- История действий -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-people"></i> Рефералы</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($referralDetails)): ?>
                        <p class="text-muted">У пользователя нет рефералов</p>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-sm table-striped">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Username</th>
                                        <th>Всего заработано</th>
                                        <th>Количество выводов</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($referralDetails as $referral): ?>
                                        <tr>
                                            <td><a href="user_detail.php?id=<?php echo $referral['id']; ?>"><?php echo $referral['id']; ?></a></td>
                                            <td><?php echo htmlspecialchars($referral['username']); ?></td>
                                            <td><?php echo $referral['total_earned']; ?></td>
                                            <td><?php echo $referral['withdrawals_count']; ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-clock-history"></i> История действий</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($auditHistory)): ?>
                        <p class="text-muted">Нет записей в истории действий</p>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-sm table-striped">
                                <thead>
                                    <tr>
                                        <th>Дата</th>
                                        <th>Действие</th>
                                        <th>Администратор</th>
                                        <th>Детали</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($auditHistory as $record): ?>
                                        <tr>
                                            <td>
                                                <small><?php echo date('Y-m-d H:i:s', strtotime($record['timestamp'])); ?></small>
                                            </td>
                                            <td>
                                                <?php
                                                $actionNames = [
                                                    'admin_update_balance' => 'Изменение баланса',
                                                    'admin_block_user' => 'Блокировка',
                                                    'admin_unblock_user' => 'Разблокировка',
                                                    'admin_delete_user' => 'Удаление',
                                                    'admin_add_user' => 'Добавление',
                                                    'admin_reset_suspicious' => 'Сброс подозрительности',
                                                    'referral_bonus' => 'Реферальный бонус',
                                                    'withdrawal_request' => 'Запрос выплаты',
                                                    'withdrawal_completed' => 'Выплата завершена',
                                                    'withdrawal_failed' => 'Выплата отклонена'
                                                ];
                                                $actionName = $actionNames[$record['action']] ?? $record['action'];
                                                ?>
                                                <span class="badge bg-primary"><?php echo htmlspecialchars($actionName); ?></span>
                                            </td>
                                            <td>
                                                <small><?php echo htmlspecialchars($record['details']['admin_username'] ?? 'Система'); ?></small>
                                            </td>
                                            <td>
                                                <small>
                                                    <?php
                                                    $details = [];
                                                    if (isset($record['details']['old_balance']) && isset($record['details']['new_balance'])) {
                                                        $details[] = "Баланс: {$record['details']['old_balance']} → {$record['details']['new_balance']}";
                                                    }
                                                    if (isset($record['details']['bonus_amount'])) {
                                                        $details[] = "Бонус: {$record['details']['bonus_amount']} монет";
                                                    }
                                                    if (isset($record['details']['withdrawal_amount'])) {
                                                        $details[] = "Сумма: {$record['details']['withdrawal_amount']} монет";
                                                    }
                                                    echo htmlspecialchars(implode(', ', $details));
                                                    ?>
                                                </small>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </main>
    </div>
</div>

<!-- Модальное окно редактирования пользователя -->
<div class="modal fade" id="editUserModal" tabindex="-1" aria-labelledby="editUserModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editUserModalLabel">Редактирование пользователя #<?php echo $userId; ?></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form method="post" action="users.php">
                    <input type="hidden" name="action" value="update_balance">
                    <input type="hidden" name="user_id" value="<?php echo $userId; ?>">

                    <div class="mb-3">
                        <label for="new_balance" class="form-label">Баланс</label>
                        <input type="number" class="form-control" id="new_balance" name="new_balance" value="<?php echo $user['balance'] ?? 0; ?>" min="0">
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Дата регистрации</label>
                        <input type="text" class="form-control" value="<?php echo isset($user['joined']) ? date('Y-m-d H:i', $user['joined']) : 'Неизвестно'; ?>" disabled>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Рефералы</label>
                        <input type="text" class="form-control" value="<?php echo $user['referrals_count'] ?? 0; ?>" disabled>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Заработано на рефералах</label>
                        <input type="text" class="form-control" value="<?php echo $user['referral_earnings'] ?? 0; ?>" disabled>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Подозрительная активность</label>
                        <input type="text" class="form-control" value="<?php echo $user['suspicious_activity'] ?? 0; ?>" disabled>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Статус</label>
                        <input type="text" class="form-control" value="<?php echo isset($user['blocked']) && $user['blocked'] ? 'Заблокирован' : 'Активен'; ?>" disabled>
                    </div>

                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">Сохранить изменения</button>

                        <?php if (isset($user['blocked']) && $user['blocked']): ?>
                            <a href="users.php?action=unblock_user&user_id=<?php echo $userId; ?>" class="btn btn-success">
                                <i class="bi bi-unlock"></i> Разблокировать пользователя
                            </a>
                        <?php else: ?>
                            <a href="users.php?action=block_user&user_id=<?php echo $userId; ?>" class="btn btn-danger">
                                <i class="bi bi-lock"></i> Заблокировать пользователя
                            </a>
                        <?php endif; ?>

                        <?php if (isset($user['suspicious_activity']) && $user['suspicious_activity'] > 0): ?>
                            <a href="users.php?action=reset_suspicious&user_id=<?php echo $userId; ?>" class="btn btn-warning">
                                <i class="bi bi-arrow-counterclockwise"></i> Сбросить подозрительность
                            </a>
                        <?php endif; ?>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<?php
// Подключаем шаблон подвала
include 'templates/footer.php';
?>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener("DOMContentLoaded", function() {
    const ctx = document.getElementById('activityChart').getContext('2d');
    let activityChart;

    const dailyBtn = document.getElementById('dailyBtn');
    const hourlyBtn = document.getElementById('hourlyBtn');
    const applyDateFilterBtn = document.getElementById('applyDateFilter');
    const dateFromInput = document.getElementById('dateFrom');
    const dateToInput = document.getElementById('dateTo');

    let currentPeriod = 'last_7_days';

    dailyBtn.addEventListener('click', () => {
        currentPeriod = 'last_7_days';
        fetchChartData();
        dailyBtn.classList.add('active');
        hourlyBtn.classList.remove('active');
    });

    hourlyBtn.addEventListener('click', () => {
        currentPeriod = 'last_24_hours';
        fetchChartData();
        hourlyBtn.classList.add('active');
        dailyBtn.classList.remove('active');
    });

    applyDateFilterBtn.addEventListener('click', () => {
        currentPeriod = 'custom_date';
        fetchChartData();
    });

    function renderChart(data, period) {
        const labels = Object.keys(data);
        let chartLabelClicks = 'Клики';
        let chartLabelViews = 'Просмотры';

        const chartData = {
            labels: labels,
            datasets: [{
                label: chartLabelClicks,
                data: labels.map(key => data[key].clicks),
                backgroundColor: 'rgba(54, 162, 235, 0.5)',
                borderColor: 'rgba(54, 162, 235, 1)',
                borderWidth: 1
            }, {
                label: chartLabelViews,
                data: labels.map(key => data[key].views),
                backgroundColor: 'rgba(75, 192, 192, 0.5)',
                borderColor: 'rgba(75, 192, 192, 1)',
                borderWidth: 1
            }]
        };

        if (activityChart) {
            activityChart.destroy();
        }

        activityChart = new Chart(ctx, {
            type: 'bar',
            data: chartData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Количество'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: period === 'last_24_hours' ? 'Время (UTC)' : 'Дата'
                        }
                    }
                },
                plugins: {
                    legend: {
                        position: 'top'
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false
                    }
                }
            }
        });
    }

    function fetchChartData() {
        let url = `user_stats_api.php?user_id=<?php echo $userId; ?>&period=${currentPeriod}`;
        if (currentPeriod === 'custom_date') {
            const dateFrom = dateFromInput.value;
            const dateTo = dateToInput.value;
            url += `&date_from=${dateFrom}&date_to=${dateTo}`;
        }

        console.log('Fetching data from URL:', url);

        fetch(url)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(result => {
                console.log('Data received from API:', result);
                if (result.success) {
                    renderChart(result.data, currentPeriod);
                } else {
                    console.error('API returned an error:', result.error);
                    alert('Ошибка API: ' + result.error);
                }
            })
            .catch(e => {
                console.error('Fetch error:', e);
                alert('Ошибка загрузки данных для графика. Подробности в консоли.');
            });
    }

    fetchChartData();
});
</script>
