<?php
header('Content-Type: application/json');

// Устанавливаем часовой пояс на UTC
date_default_timezone_set('UTC');

// Подключаем безопасный логгер
require_once __DIR__ . '/safe_json_logger.php';

$input = json_decode(file_get_contents('php://input'), true);

if (empty($input) || !isset($input['user_id']) || !isset($input['ad_type'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'error' => 'Invalid input']);
    exit;
}

// Используем безопасный логгер
$logger = new SafeJsonLogger();
$success = $logger->logAdClick(
    $input['user_id'],
    $input['ad_type'],
    $input['click_type'] ?? 'button_click',
    $input['reason'] ?? '',
    $_SERVER['REMOTE_ADDR'] ?? 'unknown',
    $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
);

if ($success) {
    echo json_encode(['success' => true]);
} else {
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => 'Failed to log click']);
}
