<?php
/**
 * Автоматическое обновление URL в настройках бота
 * Определяет текущую папку и обновляет все URL соответственно
 */

// Подключаем аутентификацию
require_once __DIR__ . '/auth.php';
session_start();
if (!isAuthenticated()) {
    http_response_code(403);
    echo json_encode(['success' => false, 'error' => 'Unauthorized']);
    exit;
}

require_once __DIR__ . '/../../includes/bot_config_loader.php';

header('Content-Type: application/json');

/**
 * Определяет текущую папку проекта на основе URL
 */
function detectCurrentFolder() {
    // Пытаемся определить папку из различных источников
    $possiblePaths = [];
    
    // 1. Из HTTP_HOST и REQUEST_URI
    if (isset($_SERVER['HTTP_HOST']) && isset($_SERVER['REQUEST_URI'])) {
        $fullUrl = 'https://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'];
        if (preg_match('#https://[^/]+/([^/]+)/#', $fullUrl, $matches)) {
            $possiblePaths[] = $matches[1];
        }
    }
    
    // 2. Из DOCUMENT_ROOT и SCRIPT_FILENAME
    if (isset($_SERVER['DOCUMENT_ROOT']) && isset($_SERVER['SCRIPT_FILENAME'])) {
        $docRoot = realpath($_SERVER['DOCUMENT_ROOT']);
        $scriptPath = realpath($_SERVER['SCRIPT_FILENAME']);
        
        if ($docRoot && $scriptPath && strpos($scriptPath, $docRoot) === 0) {
            $relativePath = substr($scriptPath, strlen($docRoot));
            $pathParts = explode('/', trim($relativePath, '/'));
            if (count($pathParts) > 0) {
                $possiblePaths[] = $pathParts[0];
            }
        }
    }
    
    // 3. Из текущего пути файла
    $currentDir = __DIR__;
    $pathParts = explode('/', $currentDir);
    foreach ($pathParts as $i => $part) {
        if ($part === 'api' && isset($pathParts[$i-1])) {
            $possiblePaths[] = $pathParts[$i-1];
            break;
        }
    }
    
    // Возвращаем наиболее вероятную папку
    $possiblePaths = array_unique($possiblePaths);
    
    // Фильтруем очевидно неправильные варианты
    $filtered = array_filter($possiblePaths, function($path) {
        return !empty($path) && 
               !in_array($path, ['api', 'admin', 'public_html', 'www', 'htdocs']) &&
               strlen($path) > 2;
    });
    
    return !empty($filtered) ? reset($filtered) : 'test3'; // fallback
}

/**
 * Обновляет URL в настройках бота
 */
function updateBotUrls($newFolder) {
    $baseUrl = "https://app.uniqpaid.com/{$newFolder}";
    
    $newSettings = [
        'WEBAPP_URL' => $baseUrl,
        'WEBHOOK_URL' => "{$baseUrl}/bot/webhook.php",
        'SUPPORT_WEBHOOK_URL' => "{$baseUrl}/api/admin/support_webhook.php"
    ];
    
    // Загружаем текущие настройки
    $currentSettings = loadBotSettings();
    
    // Объединяем с новыми URL
    $updatedSettings = array_merge($currentSettings, $newSettings);
    $updatedSettings['last_updated'] = date('Y-m-d H:i:s');
    $updatedSettings['updated_by'] = 'auto_update';
    $updatedSettings['auto_detected_folder'] = $newFolder;
    
    // Сохраняем настройки
    return saveBotSettings($updatedSettings, 'auto_update');
}

/**
 * Обновляет webhook для обоих ботов
 */
function updateBothWebhooks($settings) {
    $results = [];
    
    // Обновляем основной бот
    if (!empty($settings['TELEGRAM_BOT_TOKEN']) && !empty($settings['WEBHOOK_URL'])) {
        $url = "https://api.telegram.org/bot{$settings['TELEGRAM_BOT_TOKEN']}/setWebhook";
        $data = [
            'url' => $settings['WEBHOOK_URL'],
            'allowed_updates' => ['message', 'callback_query']
        ];
        
        $result = makeWebhookRequest($url, $data);
        $results['main_bot'] = $result;
    }
    
    // Обновляем бот поддержки
    if (!empty($settings['SUPPORT_BOT_TOKEN']) && !empty($settings['SUPPORT_WEBHOOK_URL'])) {
        $url = "https://api.telegram.org/bot{$settings['SUPPORT_BOT_TOKEN']}/setWebhook";
        $data = [
            'url' => $settings['SUPPORT_WEBHOOK_URL'],
            'allowed_updates' => ['message', 'callback_query'],
            'drop_pending_updates' => true
        ];
        
        $result = makeWebhookRequest($url, $data);
        $results['support_bot'] = $result;
    }
    
    return $results;
}

/**
 * Выполняет HTTP запрос для обновления webhook
 */
function makeWebhookRequest($url, $data) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);

    $result = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    if ($result && $httpCode === 200) {
        return json_decode($result, true);
    }

    return ['ok' => false, 'description' => "HTTP Error: {$httpCode}"];
}

try {
    $action = $_GET['action'] ?? 'detect';
    
    if ($action === 'detect') {
        // Определяем текущую папку
        $detectedFolder = detectCurrentFolder();
        $currentSettings = loadBotSettings();
        
        $response = [
            'success' => true,
            'detected_folder' => $detectedFolder,
            'current_settings' => [
                'WEBAPP_URL' => $currentSettings['WEBAPP_URL'] ?? '',
                'WEBHOOK_URL' => $currentSettings['WEBHOOK_URL'] ?? '',
                'SUPPORT_WEBHOOK_URL' => $currentSettings['SUPPORT_WEBHOOK_URL'] ?? ''
            ],
            'suggested_settings' => [
                'WEBAPP_URL' => "https://app.uniqpaid.com/{$detectedFolder}",
                'WEBHOOK_URL' => "https://app.uniqpaid.com/{$detectedFolder}/bot/webhook.php",
                'SUPPORT_WEBHOOK_URL' => "https://app.uniqpaid.com/{$detectedFolder}/api/admin/support_webhook.php"
            ]
        ];
        
    } elseif ($action === 'update') {
        // Обновляем настройки
        $folder = $_GET['folder'] ?? detectCurrentFolder();
        
        if (updateBotUrls($folder)) {
            $newSettings = loadBotSettings();
            
            // Обновляем webhook для обоих ботов
            $webhookResults = updateBothWebhooks($newSettings);
            
            $response = [
                'success' => true,
                'message' => 'Настройки и webhook успешно обновлены',
                'folder' => $folder,
                'updated_settings' => [
                    'WEBAPP_URL' => $newSettings['WEBAPP_URL'],
                    'WEBHOOK_URL' => $newSettings['WEBHOOK_URL'],
                    'SUPPORT_WEBHOOK_URL' => $newSettings['SUPPORT_WEBHOOK_URL']
                ],
                'webhook_results' => $webhookResults
            ];
        } else {
            throw new Exception('Не удалось сохранить настройки');
        }
        
    } else {
        throw new Exception('Неверный параметр action');
    }
    
} catch (Exception $e) {
    $response = [
        'success' => false,
        'error' => $e->getMessage()
    ];
}

echo json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
