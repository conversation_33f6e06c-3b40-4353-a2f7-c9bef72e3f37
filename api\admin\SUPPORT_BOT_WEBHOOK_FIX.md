# Исправление проблемы с webhook бота поддержки

## 🚨 Проблема
При изменении папки проекта на сервере (например, с `test3` на `test4`) бот поддержки переставал работать, потому что:

1. **Webhook основного бота** обновлялся автоматически при сохранении настроек
2. **Webhook бота поддержки** НЕ обновлялся и оставался со старым URL
3. **Пути были захардкожены** в настройках по умолчанию

## ✅ Решение

### 1. **Добавлена функция обновления webhook бота поддержки**

В `api/admin/bot_settings.php` добавлена функция `updateSupportWebhookUrl()`:

```php
function updateSupportWebhookUrl($supportWebhookUrl) {
    // Автоматически обновляет webhook бота поддержки
    // при сохранении настроек в админке
}
```

### 2. **Автоматическое обновление при сохранении настроек**

Теперь при сохранении настроек в админке обновляются webhook **обоих** ботов:

```php
// Обновляем webhook основного бота
if (isset($post_config['WEBHOOK_URL'])) {
    updateWebhookUrl($post_config['WEBHOOK_URL']);
}

// Обновляем webhook бота поддержки  
if (isset($post_config['SUPPORT_WEBHOOK_URL'])) {
    updateSupportWebhookUrl($post_config['SUPPORT_WEBHOOK_URL']);
}
```

### 3. **Инструменты диагностики и исправления**

#### A. Кнопки проверки webhook бота поддержки
- **🔍 Проверить** - показывает текущий статус webhook
- **🔧 Исправить** - принудительно обновляет webhook

#### B. Автоматическое обновление URL
- **🔍 Определить текущую папку** - автоматически определяет папку проекта
- **🚀 Обновить все URL автоматически** - обновляет все URL и webhook

## 🛠 Новые файлы

### `api/admin/fix_support_webhook.php`
Утилита для диагностики и исправления webhook бота поддержки:

- `?action=info` - получить информацию о webhook
- `?action=update` - обновить webhook

### `api/admin/auto_update_urls.php`
Утилита для автоматического обновления URL при смене папки:

- `?action=detect` - определить текущую папку
- `?action=update` - обновить все URL и webhook

## 🎯 Как использовать

### При смене папки проекта:

1. **Зайдите в админку** → Настройки бота
2. **Нажмите "🔍 Определить текущую папку"** - система покажет текущие и предлагаемые настройки
3. **Нажмите "🚀 Обновить все URL автоматически"** - система обновит все URL и webhook
4. **Проверьте результат** кнопкой "🔍 Проверить" у настроек бота поддержки

### При проблемах с ботом поддержки:

1. **Зайдите в админку** → Настройки бота
2. **Найдите поле SUPPORT_WEBHOOK_URL**
3. **Нажмите "🔍"** - проверить текущий статус
4. **Нажмите "🔧"** - исправить webhook

## 📋 Что изменилось в коде

### Измененные файлы:
- `api/admin/bot_settings.php` - добавлена поддержка webhook бота поддержки
- `includes/bot_config_loader.php` - настройки по умолчанию (если нужно)

### Новые файлы:
- `api/admin/fix_support_webhook.php` - диагностика webhook бота поддержки
- `api/admin/auto_update_urls.php` - автоматическое обновление URL
- `api/admin/SUPPORT_BOT_WEBHOOK_FIX.md` - эта документация

## 🔄 Принцип работы

1. **Динамическая загрузка настроек** - все настройки загружаются из `database/bot_settings.json`
2. **Автоматическое обновление webhook** - при изменении URL в админке webhook обновляется автоматически
3. **Диагностика проблем** - кнопки позволяют быстро проверить и исправить проблемы
4. **Автоопределение папки** - система может автоматически определить текущую папку проекта

## 🎉 Результат

Теперь при смене папки проекта:
- ✅ Основной бот продолжает работать
- ✅ Бот поддержки продолжает работать  
- ✅ Все URL обновляются автоматически
- ✅ Webhook обоих ботов обновляются
- ✅ Есть инструменты диагностики проблем

**Проблема полностью решена!** 🚀
