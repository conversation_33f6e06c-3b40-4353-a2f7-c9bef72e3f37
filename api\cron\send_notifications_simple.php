<?php
/**
 * api/cron/send_notifications_simple.php
 * Упрощенная версия cron-скрипта только с ротацией логов
 * Запускается каждый день в 10:00 UTC
 */

// Включаем логирование
ini_set('display_errors', 1);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/../critical-errors.log');
error_reporting(E_ALL);

// Логирование начала работы скрипта
$logFile = __DIR__ . '/notifications.log';
function logMessage($message) {
    global $logFile;
    $timestamp = date('Y-m-d H:i:s');
    file_put_contents($logFile, "[$timestamp] $message\n", FILE_APPEND | LOCK_EX);
    echo "[$timestamp] $message\n";
}

try {
    logMessage("=== НАЧАЛО РОТАЦИИ ЛОГОВ ===");
    
    // Запускаем ротацию логов
    $rotationScript = __DIR__ . '/log_rotation.php';
    
    if (file_exists($rotationScript)) {
        // Выполняем скрипт ротации
        ob_start();
        include $rotationScript;
        $rotationResult = ob_get_clean();
        
        // Парсим результат
        $result = json_decode($rotationResult, true);
        
        if ($result && $result['success']) {
            logMessage("Ротация логов выполнена успешно:");
            logMessage("- Ротация по размеру: " . ($result['rotated_by_size'] ? 'Да' : 'Нет'));
            logMessage("- Ротация по дате: " . ($result['rotated_by_date'] ? 'Да' : 'Нет'));
            logMessage("- Удалено архивов: " . $result['deleted_archives']);
            logMessage("- Всего архивов: " . $result['archive_stats']['count']);
            logMessage("- Размер архивов: " . round($result['archive_stats']['total_size'] / 1024 / 1024, 2) . " МБ");
        } else {
            $error = $result ? $result['error'] : 'Неизвестная ошибка';
            logMessage("ОШИБКА ротации логов: " . $error);
        }
    } else {
        logMessage("ПРЕДУПРЕЖДЕНИЕ: Скрипт ротации логов не найден: " . $rotationScript);
    }
    
    logMessage("=== ЗАВЕРШЕНИЕ РОТАЦИИ ЛОГОВ ===");
    
} catch (Exception $e) {
    logMessage("КРИТИЧЕСКАЯ ОШИБКА: " . $e->getMessage());
    exit(1);
}

exit(0);
?>
