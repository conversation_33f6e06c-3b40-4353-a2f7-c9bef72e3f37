<?php
/**
 * CRON ЗАДАЧА ДЛЯ ОЧИСТКИ BACKUP'ОВ
 * Запускается ежедневно для очистки старых backup'ов
 */

require_once __DIR__ . '/../backup_manager.php';

// Логируем запуск
error_log("CRON: Запуск очистки backup'ов");

try {
    $manager = new BackupManager();
    
    // Получаем информацию о backup'ах до очистки
    $infoBefore = $manager->showInfo();
    
    // Очищаем backup'ы старше 7 дней
    $deleted = $manager->cleanOld(604800); // 7 дней
    
    // Получаем информацию после очистки
    $infoAfter = $manager->showInfo();
    
    // Логируем результат
    if ($deleted > 0) {
        error_log("CRON: Очистка backup'ов завершена. Удалено: $deleted, осталось: {$infoAfter['count']}");
    } else {
        error_log("CRON: Очистка backup'ов - нет файлов для удаления. Всего backup'ов: {$infoAfter['count']}");
    }
    
    // Предупреждение если backup'ов слишком много
    if ($infoAfter['count'] > 15) {
        error_log("CRON WARNING: Много backup'ов ({$infoAfter['count']}), рассмотрите уменьшение периода хранения");
    }
    
    // Предупреждение если backup'ы занимают много места
    if ($infoAfter['total_size_mb'] > 100) {
        error_log("CRON WARNING: Backup'ы занимают много места ({$infoAfter['total_size_mb']} MB)");
    }
    
} catch (Exception $e) {
    error_log("CRON ERROR: Ошибка при очистке backup'ов: " . $e->getMessage());
}

?>
