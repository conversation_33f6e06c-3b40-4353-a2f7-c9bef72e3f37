<?php
/**
 * СКРИПТ ДЛЯ БЕЗОПАСНОГО СЛИЯНИЯ ДАННЫХ ИЗ user_data1.json
 * Восстанавливает потерянные данные пользователей
 */

require_once __DIR__ . '/config.php';
require_once __DIR__ . '/secure_data_manager.php';

echo "=== БЕЗОПАСНОЕ СЛИЯНИЕ ДАННЫХ ПОЛЬЗОВАТЕЛЕЙ ===\n\n";

// Инициализируем безопасный менеджер данных
$secureManager = new SecureDataManager(USER_DATA_FILE);

// Путь к дополнительному файлу
$additionalFile = __DIR__ . '/user_data1.json';

// Проверяем существование файлов
echo "Проверка файлов:\n";
echo "- Основной файл: " . USER_DATA_FILE . " - " . (file_exists(USER_DATA_FILE) ? "✅ Существует" : "❌ Не найден") . "\n";
echo "- Дополнительный файл: " . $additionalFile . " - " . (file_exists($additionalFile) ? "✅ Существует" : "❌ Не найден") . "\n\n";

if (!file_exists($additionalFile)) {
    echo "❌ ОШИБКА: Файл user_data1.json не найден!\n";
    exit(1);
}

// Показываем статистику до слияния
echo "Статистика ДО слияния:\n";

$mainData = $secureManager->loadUserData();
if ($mainData === false) {
    echo "❌ ОШИБКА: Не удалось загрузить основные данные!\n";
    exit(1);
}

$additionalContent = file_get_contents($additionalFile);
if ($additionalContent === false) {
    echo "❌ ОШИБКА: Не удалось прочитать дополнительный файл!\n";
    exit(1);
}

$additionalData = json_decode($additionalContent, true);
if (json_last_error() !== JSON_ERROR_NONE) {
    echo "❌ ОШИБКА: Дополнительный файл содержит невалидный JSON: " . json_last_error_msg() . "\n";
    exit(1);
}

echo "- Пользователей в основном файле: " . count($mainData) . "\n";
echo "- Пользователей в дополнительном файле: " . count($additionalData) . "\n";

// Анализируем пересечения
$commonUsers = array_intersect(array_keys($mainData), array_keys($additionalData));
$newUsers = array_diff(array_keys($additionalData), array_keys($mainData));

echo "- Общих пользователей: " . count($commonUsers) . "\n";
echo "- Новых пользователей: " . count($newUsers) . "\n\n";

// Показываем примеры новых пользователей
if (!empty($newUsers)) {
    echo "Примеры новых пользователей:\n";
    $examples = array_slice($newUsers, 0, 5);
    foreach ($examples as $userId) {
        $userData = $additionalData[$userId];
        echo "- ID: $userId, Баланс: " . ($userData['balance'] ?? 'N/A') . ", Заработано: " . ($userData['total_earned'] ?? 'N/A') . "\n";
    }
    echo "\n";
}

// Запрашиваем подтверждение
echo "Вы хотите продолжить слияние? (y/N): ";
$handle = fopen("php://stdin", "r");
$line = fgets($handle);
fclose($handle);

if (trim(strtolower($line)) !== 'y') {
    echo "Операция отменена пользователем.\n";
    exit(0);
}

echo "\n=== НАЧИНАЕМ СЛИЯНИЕ ===\n";

// Выполняем слияние
$result = $secureManager->mergeAdditionalData($additionalFile);

if ($result) {
    echo "✅ СЛИЯНИЕ УСПЕШНО ЗАВЕРШЕНО!\n\n";
    
    // Показываем статистику после слияния
    $mergedData = $secureManager->loadUserData();
    echo "Статистика ПОСЛЕ слияния:\n";
    echo "- Общее количество пользователей: " . count($mergedData) . "\n";
    echo "- Добавлено новых пользователей: " . (count($mergedData) - count($mainData)) . "\n\n";
    
    echo "=== РЕКОМЕНДАЦИИ ===\n";
    echo "1. Проверьте работу приложения\n";
    echo "2. Убедитесь, что все данные корректны\n";
    echo "3. Файл user_data1.json переименован с суффиксом _merged\n";
    echo "4. Созданы бэкапы всех изменений\n\n";
    
    echo "✅ Все готово! Данные пользователей восстановлены.\n";
    
} else {
    echo "❌ ОШИБКА: Слияние не удалось!\n";
    echo "Проверьте логи для получения подробной информации.\n";
    exit(1);
}
?>
