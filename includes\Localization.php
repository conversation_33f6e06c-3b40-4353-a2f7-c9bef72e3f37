<?php
/**
 * Localization.php
 * Класс для работы с локализацией
 */

class Localization {
    private static $instance = null;
    private $currentLanguage = 'ru';
    private $translations = [];
    private $fallbackLanguage = 'en';

    // Русскоязычные страны и их коды
    private $russianSpeakingCountries = [
        'RU', // Россия
        'BY', // Беларусь
        'KZ', // Казахстан
        'KG', // Киргизия
        'TJ', // Таджикистан
        'UZ', // Узбекистан
        'AM', // Армения
        'AZ', // Азербайджан
        'GE', // Грузия
        'MD', // Молдова
        'UA'  // Украина
    ];

    private function __construct() {
        $this->loadTranslations();
    }

    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Загружает переводы для всех языков
     */
    private function loadTranslations() {
        $localesDir = __DIR__ . '/../locales/';

        // Загружаем русский язык
        $ruFile = $localesDir . 'ru.json';
        if (file_exists($ruFile)) {
            $this->translations['ru'] = json_decode(file_get_contents($ruFile), true);
        }

        // Загружаем английский язык
        $enFile = $localesDir . 'en.json';
        if (file_exists($enFile)) {
            $this->translations['en'] = json_decode(file_get_contents($enFile), true);
        }
    }

    /**
     * Определяет язык пользователя по данным Telegram
     */
    public function detectLanguage($telegramUser) {
        // Список русскоязычных кодов языков
        $russianLanguageCodes = ['ru', 'be', 'uk', 'kk', 'ky', 'tg', 'uz', 'hy', 'az', 'ka', 'ro'];

        // По умолчанию английский
        $detectedLanguage = 'en';

        // Проверяем язык интерфейса Telegram
        if (isset($telegramUser['language_code'])) {
            $langCode = strtolower($telegramUser['language_code']);

            // Если язык входит в список русскоязычных - ставим русский
            if (in_array($langCode, $russianLanguageCodes)) {
                $detectedLanguage = 'ru';
            }
        }

        // Если язык еще не определен как русский, проверяем страну
        if ($detectedLanguage === 'en' && isset($telegramUser['country_code'])) {
            $countryCode = strtoupper($telegramUser['country_code']);
            if (in_array($countryCode, $this->russianSpeakingCountries)) {
                $detectedLanguage = 'ru';
            }
        }

        // Если язык все еще не определен, пробуем определить по IP
        if ($detectedLanguage === 'en') {
            $ipLanguage = $this->detectLanguageByIP();
            if ($ipLanguage === 'ru') {
                $detectedLanguage = 'ru';
            }
        }

        return $detectedLanguage;
    }

    /**
     * Определяет язык по IP адресу пользователя
     */
    public function detectLanguageByIP() {
        $userIP = $this->getUserIP();

        if (!$userIP || $userIP === '127.0.0.1' || $userIP === '::1') {
            return 'en'; // Локальный IP - возвращаем английский
        }

        try {
            // Используем бесплатный сервис для определения страны по IP
            $geoData = $this->getCountryByIP($userIP);

            if ($geoData && isset($geoData['country_code'])) {
                $countryCode = strtoupper($geoData['country_code']);

                if (in_array($countryCode, $this->russianSpeakingCountries)) {
                    return 'ru';
                }
            }
        } catch (Exception $e) {
            error_log("Ошибка определения языка по IP: " . $e->getMessage());
        }

        return 'en';
    }

    /**
     * Получает реальный IP адрес пользователя
     */
    private function getUserIP() {
        // Проверяем различные заголовки для получения реального IP
        $ipKeys = [
            'HTTP_CF_CONNECTING_IP',     // Cloudflare
            'HTTP_CLIENT_IP',            // Прокси
            'HTTP_X_FORWARDED_FOR',      // Балансировщик нагрузки
            'HTTP_X_FORWARDED',          // Прокси
            'HTTP_X_CLUSTER_CLIENT_IP',  // Кластер
            'HTTP_FORWARDED_FOR',        // Прокси
            'HTTP_FORWARDED',            // Прокси
            'REMOTE_ADDR'                // Стандартный
        ];

        foreach ($ipKeys as $key) {
            if (array_key_exists($key, $_SERVER) && !empty($_SERVER[$key])) {
                $ips = explode(',', $_SERVER[$key]);
                $ip = trim($ips[0]);

                // Проверяем, что IP валидный и не приватный
                if (filter_var($ip, FILTER_VALIDATE_IP) && !$this->isPrivateIP($ip)) {
                    return $ip;
                }
            }
        }

        return $_SERVER['REMOTE_ADDR'] ?? null;
    }

    /**
     * Проверяет, является ли IP приватным
     */
    private function isPrivateIP($ip) {
        $parts = explode('.', $ip);
        if (count($parts) !== 4) return true;

        $first = (int)$parts[0];
        $second = (int)$parts[1];

        return (
            $first === 10 ||
            ($first === 172 && $second >= 16 && $second <= 31) ||
            ($first === 192 && $second === 168) ||
            $first === 127
        );
    }

    /**
     * Получает информацию о стране по IP адресу
     */
    private function getCountryByIP($ip) {
        try {
            // Используем бесплатный API ipapi.co (1000 запросов в день)
            $url = "http://ipapi.co/{$ip}/json/";

            $context = stream_context_create([
                'http' => [
                    'timeout' => 3, // 3 секунды таймаут
                    'user_agent' => 'Mozilla/5.0 (compatible; TelegramBot/1.0)'
                ]
            ]);

            $response = @file_get_contents($url, false, $context);

            if ($response !== false) {
                $data = json_decode($response, true);

                if (isset($data['country_code']) && !isset($data['error'])) {
                    return [
                        'country_code' => $data['country_code'],
                        'country_name' => $data['country_name'] ?? '',
                        'city' => $data['city'] ?? ''
                    ];
                }
            }
        } catch (Exception $e) {
            error_log("Ошибка запроса к ipapi.co: " . $e->getMessage());
        }

        // Fallback: пробуем другой сервис
        try {
            $url = "http://ip-api.com/json/{$ip}?fields=status,country,countryCode";

            $context = stream_context_create([
                'http' => [
                    'timeout' => 3,
                    'user_agent' => 'Mozilla/5.0 (compatible; TelegramBot/1.0)'
                ]
            ]);

            $response = @file_get_contents($url, false, $context);

            if ($response !== false) {
                $data = json_decode($response, true);

                if (isset($data['status']) && $data['status'] === 'success' && isset($data['countryCode'])) {
                    return [
                        'country_code' => $data['countryCode'],
                        'country_name' => $data['country'] ?? ''
                    ];
                }
            }
        } catch (Exception $e) {
            error_log("Ошибка запроса к ip-api.com: " . $e->getMessage());
        }

        return null;
    }

    /**
     * Устанавливает текущий язык
     */
    public function setLanguage($language) {
        if (isset($this->translations[$language])) {
            $this->currentLanguage = $language;
        }
    }

    /**
     * Получает текущий язык
     */
    public function getCurrentLanguage() {
        return $this->currentLanguage;
    }

    /**
     * Получает перевод по ключу
     */
    public function get($key, $params = []) {
        $keys = explode('.', $key);
        $translation = $this->translations[$this->currentLanguage] ?? [];

        // Ищем перевод по ключу
        foreach ($keys as $k) {
            if (isset($translation[$k])) {
                $translation = $translation[$k];
            } else {
                // Если не найден, пробуем fallback язык
                $translation = $this->translations[$this->fallbackLanguage] ?? [];
                foreach ($keys as $fallbackKey) {
                    if (isset($translation[$fallbackKey])) {
                        $translation = $translation[$fallbackKey];
                    } else {
                        return $key; // Возвращаем ключ, если перевод не найден
                    }
                }
                break;
            }
        }

        // Если это не строка, возвращаем ключ
        if (!is_string($translation)) {
            return $key;
        }

        // Заменяем параметры в строке
        foreach ($params as $param => $value) {
            $translation = str_replace('{' . $param . '}', $value, $translation);
        }

        return $translation;
    }

    /**
     * Определяет язык по стране из Telegram Web App
     */
    public function detectLanguageFromWebApp($initData) {
        // Парсим initData для получения информации о пользователе
        parse_str($initData, $data);

        if (isset($data['user'])) {
            $user = json_decode($data['user'], true);
            return $this->detectLanguage($user);
        }

        return 'en';
    }

    /**
     * Проверяет, является ли страна русскоязычной
     */
    public function isRussianSpeakingCountry($countryCode) {
        return in_array(strtoupper($countryCode), $this->russianSpeakingCountries);
    }
}
?>
