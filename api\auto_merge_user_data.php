<?php
/**
 * АВТОМАТИЧЕСКОЕ ОБЪЕДИНЕНИЕ ДАННЫХ ПОЛЬЗОВАТЕЛЕЙ
 * Объединяет данные из user_data1.json с основным user_data.json без интерактивного ввода
 */

require_once __DIR__ . '/secure_user_data_manager.php';

function logMessage($message) {
    echo $message . "\n";
    error_log("MERGE: " . $message);
}

logMessage("🔄 НАЧИНАЕМ АВТОМАТИЧЕСКОЕ ОБЪЕДИНЕНИЕ ДАННЫХ ПОЛЬЗОВАТЕЛЕЙ");
logMessage("==========================================");

// Пути к файлам
$mainFile = __DIR__ . '/user_data.json';
$additionalFile = __DIR__ . '/user_data1.json';
$backupFile = __DIR__ . '/user_data_before_merge_' . date('Y-m-d_H-i-s') . '.json';

// Проверяем существование файлов
if (!file_exists($mainFile)) {
    logMessage("❌ ОШИБКА: Основной файл user_data.json не найден!");
    exit(1);
}

if (!file_exists($additionalFile)) {
    logMessage("❌ ОШИБКА: Дополнительный файл user_data1.json не найден!");
    exit(1);
}

// Создаем backup основного файла
logMessage("📦 Создаем backup основного файла...");
if (!copy($mainFile, $backupFile)) {
    logMessage("❌ ОШИБКА: Не удалось создать backup!");
    exit(1);
}
logMessage("✅ Backup создан: " . basename($backupFile));

// Загружаем данные из основного файла
logMessage("📖 Загружаем данные из основного файла...");
$mainData = SecureUserDataManager::getInstance()->loadUserData();
if (!is_array($mainData)) {
    logMessage("❌ ОШИБКА: Не удалось загрузить основные данные!");
    exit(1);
}
logMessage("✅ Загружено пользователей из основного файла: " . count($mainData));

// Загружаем данные из дополнительного файла
logMessage("📖 Загружаем данные из дополнительного файла...");
$additionalContent = file_get_contents($additionalFile);
if ($additionalContent === false) {
    logMessage("❌ ОШИБКА: Не удалось прочитать дополнительный файл!");
    exit(1);
}

$additionalData = json_decode($additionalContent, true);
if (!is_array($additionalData)) {
    logMessage("❌ ОШИБКА: Дополнительный файл содержит некорректные данные!");
    exit(1);
}
logMessage("✅ Загружено пользователей из дополнительного файла: " . count($additionalData));

// Анализируем данные для объединения
logMessage("\n🔍 АНАЛИЗ ДАННЫХ:");
$newUsers = 0;
$existingUsers = 0;
$conflicts = [];

foreach ($additionalData as $userId => $userData) {
    if (isset($mainData[$userId])) {
        $existingUsers++;
        
        // Проверяем на конфликты данных
        $mainBalance = $mainData[$userId]['balance'] ?? 0;
        $additionalBalance = $userData['balance'] ?? 0;
        
        if ($mainBalance != $additionalBalance) {
            $conflicts[] = [
                'user_id' => $userId,
                'main_balance' => $mainBalance,
                'additional_balance' => $additionalBalance
            ];
        }
    } else {
        $newUsers++;
    }
}

logMessage("👥 Новые пользователи: $newUsers");
logMessage("🔄 Существующие пользователи: $existingUsers");
logMessage("⚠️  Конфликты данных: " . count($conflicts));

if (!empty($conflicts)) {
    logMessage("\n⚠️  ОБНАРУЖЕНЫ КОНФЛИКТЫ:");
    foreach ($conflicts as $conflict) {
        logMessage("   Пользователь {$conflict['user_id']}: основной баланс = {$conflict['main_balance']}, дополнительный = {$conflict['additional_balance']}");
    }
}

// Выполняем объединение автоматически
logMessage("\n🔄 ВЫПОЛНЯЕМ АВТОМАТИЧЕСКОЕ ОБЪЕДИНЕНИЕ...");
$mergedData = $mainData;
$addedUsers = 0;
$updatedUsers = 0;

foreach ($additionalData as $userId => $userData) {
    if (!isset($mergedData[$userId])) {
        // Добавляем нового пользователя
        $mergedData[$userId] = $userData;
        $addedUsers++;
        logMessage("➕ Добавлен пользователь: $userId");
    } else {
        // Обновляем существующего пользователя (объединяем данные)
        $existing = $mergedData[$userId];
        $updated = false;
        
        // Объединяем поля, которых нет в основных данных
        foreach ($userData as $key => $value) {
            if (!isset($existing[$key])) {
                $mergedData[$userId][$key] = $value;
                $updated = true;
            } else {
                // Для некоторых полей берем более новые данные
                if ($key === 'last_activity' && is_numeric($value) && is_numeric($existing[$key])) {
                    if ($value > $existing[$key]) {
                        $mergedData[$userId][$key] = $value;
                        $updated = true;
                    }
                }
                // Для массивов объединяем уникальные значения
                if (is_array($value) && is_array($existing[$key])) {
                    $merged = array_unique(array_merge($existing[$key], $value));
                    if (count($merged) > count($existing[$key])) {
                        $mergedData[$userId][$key] = array_values($merged);
                        $updated = true;
                    }
                }
            }
        }
        
        if ($updated) {
            $updatedUsers++;
            logMessage("📝 Дополнены данные пользователя: $userId");
        }
    }
}

// Сохраняем объединенные данные
logMessage("\n💾 Сохраняем объединенные данные...");
if (!SecureUserDataManager::getInstance()->saveUserData($mergedData)) {
    logMessage("❌ КРИТИЧЕСКАЯ ОШИБКА: Не удалось сохранить объединенные данные!");
    logMessage("🔄 Восстанавливаем из backup...");
    copy($backupFile, $mainFile);
    logMessage("✅ Данные восстановлены из backup.");
    exit(1);
}

// Создаем backup дополнительного файла
$additionalBackup = $additionalFile . '_merged_' . date('Y-m-d_H-i-s') . '.backup';
copy($additionalFile, $additionalBackup);
logMessage("📦 Создан backup дополнительного файла: " . basename($additionalBackup));

logMessage("\n✅ ОБЪЕДИНЕНИЕ ЗАВЕРШЕНО УСПЕШНО!");
logMessage("==========================================");
logMessage("📊 СТАТИСТИКА:");
logMessage("   👥 Добавлено новых пользователей: $addedUsers");
logMessage("   🔄 Обновлено существующих: $updatedUsers");
logMessage("   📦 Всего пользователей в базе: " . count($mergedData));
logMessage("   💾 Backup основного файла: " . basename($backupFile));
logMessage("   💾 Backup дополнительного файла: " . basename($additionalBackup));
logMessage("\n🎉 Данные пользователей успешно объединены и защищены!");

?>
