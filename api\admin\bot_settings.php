<?php
/**
 * api/admin/bot_settings.php
 * Финальная, очищенная версия страницы настроек бота
 */

// Безопасность и аутентификация
require_once __DIR__ . '/auth.php';
session_start();
if (!isAuthenticated()) {
    header('Location: login.php');
    exit;
}

// Зависимости
require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../../includes/bot_config_loader.php';

$message = '';
$error = '';

// --- Пути к файлам настроек ---
$botTextsFile = __DIR__ . '/../../bot/bot_texts.json';
$buttonVisibilityFile = __DIR__ . '/../../bot/button_visibility.json';
$configFile = __DIR__ . '/../config.php';

// --- Обработка сохранения настроек ---
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // 1. Сохранение настроек бота в JSON файл
    try {
        $post_config = $_POST['config'] ?? [];

        if (!empty($post_config)) {
            // Получаем имя пользователя для логирования
            $updatedBy = $_SESSION['admin_username'] ?? 'admin';

            // Сохраняем настройки в JSON
            if (saveBotSettings($post_config, $updatedBy)) {
                $message .= 'Настройки бота сохранены в JSON. ';

                // Также обновляем webhook URL если он изменился
                if (isset($post_config['WEBHOOK_URL'])) {
                    $webhookResult = updateWebhookUrl($post_config['WEBHOOK_URL']);
                    if ($webhookResult) {
                        $message .= 'Webhook основного бота обновлен. ';
                    } else {
                        $error .= 'Ошибка обновления webhook основного бота. ';
                    }
                }

                // Обновляем webhook бота поддержки если он изменился
                if (isset($post_config['SUPPORT_WEBHOOK_URL'])) {
                    $supportWebhookResult = updateSupportWebhookUrl($post_config['SUPPORT_WEBHOOK_URL']);
                    if ($supportWebhookResult) {
                        $message .= 'Webhook бота поддержки обновлен. ';
                    } else {
                        $error .= 'Ошибка обновления webhook бота поддержки. ';
                    }
                }
            } else {
                throw new Exception("Не удалось сохранить настройки в JSON файл");
            }
        }

    } catch (Exception $e) {
        $error .= 'Ошибка сохранения настроек: ' . $e->getMessage() . ' ';
    }

    // 2. Сохранение видимости кнопок
    // Используем жестко заданный список ключей, чтобы избежать ошибок, если файл пуст или поврежден
    $buttonKeys = ['launch_app', 'my_balance', 'friends', 'statistics', 'help'];
    $buttons_data = [];
    foreach ($buttonKeys as $key) {
        // Если чекбокс отмечен, он будет в POST. Если нет, то нет.
        $buttons_data[$key] = isset($_POST['buttons'][$key]);
    }
    if (file_put_contents($buttonVisibilityFile, json_encode($buttons_data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE))) {
        $message .= 'Настройки видимости кнопок сохранены. ';
    } else {
        $error .= 'Ошибка сохранения видимости кнопок. ';
    }

    // 3. Сохранение текстов бота
    if (isset($_POST['texts'])) {
        $updatedTexts = json_decode(file_get_contents($botTextsFile), true);
        foreach ($_POST['texts'] as $lang => $groups) {
            foreach ($groups as $key_path => $text) {
                $keys = explode('[', str_replace(']', '', $key_path));
                $temp = &$updatedTexts[$lang];
                foreach ($keys as $key) {
                    if (!isset($temp[$key])) $temp[$key] = [];
                    $temp = &$temp[$key];
                }
                $temp = $text;
            }
        }
        if (file_put_contents($botTextsFile, json_encode($updatedTexts, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE))) {
            $message .= 'Тексты бота сохранены.';
        } else {
            $error .= 'Ошибка сохранения текстов бота.';
        }
    }
    
    if (function_exists('opcache_invalidate')) opcache_invalidate($configFile, true);
    header("Location: " . $_SERVER['PHP_SELF'] . "?save=success");
    exit;
}

if(isset($_GET['save']) && $_GET['save'] == 'success'){
    $message = "Настройки успешно сохранены!";
}

// --- Загрузка текущих настроек ---
$botTexts = file_exists($botTextsFile) ? json_decode(file_get_contents($botTextsFile), true) : [];
$defaultButtons = ['launch_app' => true, 'my_balance' => true, 'friends' => true, 'statistics' => true, 'help' => true];
$buttonVisibility = file_exists($buttonVisibilityFile) ? json_decode(file_get_contents($buttonVisibilityFile), true) : $defaultButtons;

// Загружаем настройки бота из JSON файла
$botSettingsFromJson = loadBotSettings();
$botSettings = [
    'TELEGRAM_BOT_TOKEN' => $botSettingsFromJson['TELEGRAM_BOT_TOKEN'] ?? '',
    'BOT_USERNAME' => $botSettingsFromJson['BOT_USERNAME'] ?? '',
    'WEBAPP_URL' => $botSettingsFromJson['WEBAPP_URL'] ?? '',
    'WEBHOOK_URL' => $botSettingsFromJson['WEBHOOK_URL'] ?? '',
    'SUPPORT_BOT_TOKEN' => $botSettingsFromJson['SUPPORT_BOT_TOKEN'] ?? '',
    'SUPPORT_BOT_USERNAME' => $botSettingsFromJson['SUPPORT_BOT_USERNAME'] ?? '',
    'SUPPORT_WEBHOOK_URL' => $botSettingsFromJson['SUPPORT_WEBHOOK_URL'] ?? '',
];

/**
 * Функция для обновления webhook URL основного бота
 */
function updateWebhookUrl($webhookUrl) {
    try {
        // Загружаем настройки бота
        $botSettings = loadBotSettings();
        $botToken = $botSettings['TELEGRAM_BOT_TOKEN'];

        // Отправляем запрос на обновление webhook
        $url = "https://api.telegram.org/bot{$botToken}/setWebhook";
        $data = [
            'url' => $webhookUrl,
            'allowed_updates' => ['message', 'callback_query']
        ];

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);

        $result = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($result && $httpCode === 200) {
            $response = json_decode($result, true);
            return $response && $response['ok'];
        }

        return false;
    } catch (Exception $e) {
        error_log("Ошибка обновления webhook основного бота: " . $e->getMessage());
        return false;
    }
}

/**
 * Функция для обновления webhook URL бота поддержки
 */
function updateSupportWebhookUrl($supportWebhookUrl) {
    try {
        // Загружаем настройки бота
        $botSettings = loadBotSettings();
        $supportBotToken = $botSettings['SUPPORT_BOT_TOKEN'];

        if (empty($supportBotToken)) {
            error_log("Ошибка: токен бота поддержки не найден");
            return false;
        }

        // Отправляем запрос на обновление webhook бота поддержки
        $url = "https://api.telegram.org/bot{$supportBotToken}/setWebhook";
        $data = [
            'url' => $supportWebhookUrl,
            'allowed_updates' => ['message', 'callback_query'],
            'drop_pending_updates' => true
        ];

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);

        $result = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($result && $httpCode === 200) {
            $response = json_decode($result, true);
            if ($response && $response['ok']) {
                error_log("SUCCESS: Webhook бота поддержки обновлен: " . $supportWebhookUrl);
                return true;
            } else {
                error_log("ERROR: Telegram API вернул ошибку для бота поддержки: " . json_encode($response));
            }
        } else {
            error_log("ERROR: HTTP ошибка при обновлении webhook бота поддержки: " . $httpCode);
        }

        return false;
    } catch (Exception $e) {
        error_log("Ошибка обновления webhook бота поддержки: " . $e->getMessage());
        return false;
    }
}

include 'templates/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include 'templates/sidebar.php'; ?>

        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">Настройки Telegram Бота</h1>
            </div>

            <?php if ($message): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?php echo htmlspecialchars($message); ?><button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>
            <?php if ($error): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?php echo htmlspecialchars($error); ?><button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <form method="POST">
                <ul class="nav nav-tabs" id="main-tabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="settings-tab" data-bs-toggle="tab" data-bs-target="#settings-panel" type="button" role="tab">⚙️ Настройки Бота</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="texts-tab" data-bs-toggle="tab" data-bs-target="#texts-panel" type="button" role="tab">✏️ Тексты Бота</button>
                    </li>
                </ul>

                <div class="tab-content p-3 border border-top-0" id="main-tabs-content">
                    <div class="tab-pane fade show active" id="settings-panel" role="tabpanel">
                        <div class="card mb-4">
                            <div class="card-header"><strong>Основные параметры бота</strong></div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="cfg_TELEGRAM_BOT_TOKEN" class="form-label">BOT_TOKEN</label>
                                    <input type="text" class="form-control" id="cfg_TELEGRAM_BOT_TOKEN" name="config[TELEGRAM_BOT_TOKEN]" value="<?= htmlspecialchars($botSettings['TELEGRAM_BOT_TOKEN']) ?>">
                                    <div class="form-text">Токен основного бота @uniqpaid_paid_bot</div>
                                </div>
                                <div class="mb-3">
                                    <label for="cfg_BOT_USERNAME" class="form-label">BOT_USERNAME</label>
                                    <input type="text" class="form-control" id="cfg_BOT_USERNAME" name="config[BOT_USERNAME]" value="<?= htmlspecialchars($botSettings['BOT_USERNAME']) ?>">
                                    <div class="form-text">Имя пользователя бота (без @)</div>
                                </div>
                                <div class="mb-3">
                                    <label for="cfg_WEBAPP_URL" class="form-label">WEBAPP_URL</label>
                                    <input type="text" class="form-control" id="cfg_WEBAPP_URL" name="config[WEBAPP_URL]" value="<?= htmlspecialchars($botSettings['WEBAPP_URL']) ?>">
                                    <div class="form-text">URL мини-приложения</div>
                                </div>
                                <div class="mb-3">
                                    <label for="cfg_WEBHOOK_URL" class="form-label">WEBHOOK_URL</label>
                                    <input type="text" class="form-control" id="cfg_WEBHOOK_URL" name="config[WEBHOOK_URL]" value="<?= htmlspecialchars($botSettings['WEBHOOK_URL']) ?>">
                                    <div class="form-text">URL для webhook основного бота</div>
                                </div>
                            </div>
                        </div>

                        <div class="card mb-4 border-warning">
                            <div class="card-header bg-warning bg-opacity-10">
                                <strong>🔧 Автоматическое обновление URL</strong>
                            </div>
                            <div class="card-body">
                                <p class="text-muted mb-3">
                                    Если вы изменили папку проекта на сервере (например, с test3 на test4),
                                    используйте эту функцию для автоматического обновления всех URL.
                                </p>
                                <div class="d-flex gap-2">
                                    <button type="button" class="btn btn-outline-info" id="detect-folder">
                                        🔍 Определить текущую папку
                                    </button>
                                    <button type="button" class="btn btn-warning" id="auto-update-urls">
                                        🚀 Обновить все URL автоматически
                                    </button>
                                </div>
                                <div id="auto-update-status" class="mt-3"></div>
                            </div>
                        </div>

                        <div class="card mb-4">
                            <div class="card-header"><strong>Настройки бота поддержки</strong></div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="cfg_SUPPORT_BOT_TOKEN" class="form-label">SUPPORT_BOT_TOKEN</label>
                                    <input type="text" class="form-control" id="cfg_SUPPORT_BOT_TOKEN" name="config[SUPPORT_BOT_TOKEN]" value="<?= htmlspecialchars($botSettings['SUPPORT_BOT_TOKEN']) ?>">
                                    <div class="form-text">Токен бота поддержки @uniqpaid_support_bot</div>
                                </div>
                                <div class="mb-3">
                                    <label for="cfg_SUPPORT_BOT_USERNAME" class="form-label">SUPPORT_BOT_USERNAME</label>
                                    <input type="text" class="form-control" id="cfg_SUPPORT_BOT_USERNAME" name="config[SUPPORT_BOT_USERNAME]" value="<?= htmlspecialchars($botSettings['SUPPORT_BOT_USERNAME']) ?>">
                                    <div class="form-text">Имя пользователя бота поддержки (без @)</div>
                                </div>
                                <div class="mb-3">
                                    <label for="cfg_SUPPORT_WEBHOOK_URL" class="form-label">SUPPORT_WEBHOOK_URL</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="cfg_SUPPORT_WEBHOOK_URL" name="config[SUPPORT_WEBHOOK_URL]" value="<?= htmlspecialchars($botSettings['SUPPORT_WEBHOOK_URL']) ?>">
                                        <button type="button" class="btn btn-outline-info" id="check-support-webhook" title="Проверить webhook бота поддержки">🔍</button>
                                        <button type="button" class="btn btn-outline-success" id="fix-support-webhook" title="Исправить webhook бота поддержки">🔧</button>
                                    </div>
                                    <div class="form-text">URL для webhook бота поддержки</div>
                                    <div id="support-webhook-status" class="mt-2"></div>
                                </div>
                            </div>
                        </div>

                        <div class="card mb-4">
                            <div class="card-header"><strong>Видимость кнопок в меню</strong></div>
                            <div class="card-body">
                                <div class="row">
                                <?php foreach ($buttonVisibility as $key => $isVisible): ?>
                                    <div class="col-md-4">
                                        <div class="form-check form-switch mb-2">
                                            <input class="form-check-input" type="checkbox" id="button_<?= $key ?>" name="buttons[<?= $key ?>]" <?= $isVisible ? 'checked' : '' ?>>
                                            <label class="form-check-label" for="button_<?= $key ?>"><?= ucfirst(str_replace('_', ' ', $key)) ?></label>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="tab-pane fade" id="texts-panel" role="tabpanel">
                        <ul class="nav nav-pills mb-3" id="text-lang-tabs" role="tablist">
                            <li class="nav-item" role="presentation"><button class="nav-link active" id="ru-text-tab" data-bs-toggle="tab" data-bs-target="#ru-texts" type="button" role="tab">Русский (RU)</button></li>
                            <li class="nav-item" role="presentation"><button class="nav-link" id="en-text-tab" data-bs-toggle="tab" data-bs-target="#en-texts" type="button" role="tab">English (EN)</button></li>
                        </ul>
                        <div class="tab-content" id="text-lang-tabs-content">
                            <div class="tab-pane fade show active" id="ru-texts" role="tabpanel"><?php render_text_accordion('ru', $botTexts); ?></div>
                            <div class="tab-pane fade" id="en-texts" role="tabpanel"><?php render_text_accordion('en', $botTexts); ?></div>
                        </div>
                    </div>
                </div>

                <div class="mt-4 mb-5">
                    <button type="submit" class="btn btn-primary w-100 p-3"><strong>Сохранить все изменения</strong></button>
                </div>
            </form>
        </main>
    </div>
</div>

<?php
function render_text_accordion($lang, $texts) {
    if (!isset($texts[$lang])) return;
    echo '<div class="accordion" id="accordion-' . $lang . '">';
    $i = 0;
    foreach ($texts[$lang] as $group_key => $group_values) {
        if (!is_array($group_values)) continue;
        echo '<div class="accordion-item">';
        echo '<h2 class="accordion-header" id="h-' . $lang . '-' . $i . '"><button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#c-' . $lang . '-' . $i . '" aria-expanded="false"><strong>' . ucfirst($group_key) . '</strong></button></h2>';
        echo '<div id="c-' . $lang . '-' . $i . '" class="accordion-collapse collapse" data-bs-parent="#accordion-' . $lang . '"><div class="accordion-body">';
        render_text_fields_recursive($lang, $group_values, $group_key);
        echo '</div></div></div>';
        $i++;
    }
    echo '</div>';
}

function render_text_fields_recursive($lang, $values, $parent_key) {
    foreach ($values as $key => $value) {
        $full_key = $parent_key . '[' . $key . ']';
        if (is_array($value)) {
            echo '<div class="ms-3 border-start ps-3 pt-2 mb-2">';
            echo '<p class="fw-bold text-primary">' . ucfirst($key) . '</p>';
            render_text_fields_recursive($lang, $value, $full_key);
            echo '</div>';
        } else {
            $id = 'text_' . $lang . '_' . str_replace(['[', ']'], ['_', ''], $full_key);
            echo '<div class="mb-3">';
            echo '<label for="' . $id . '" class="form-label"><small class="text-muted">' . $full_key . '</small></label>';
            echo '<textarea class="form-control form-control-sm" id="' . $id . '" name="texts[' . $lang . '][' . $full_key . ']" rows="2">' . htmlspecialchars($value) . '</textarea>';
            echo '</div>';
        }
    }
}

include 'templates/footer.php';
?>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const checkBtn = document.getElementById('check-support-webhook');
    const fixBtn = document.getElementById('fix-support-webhook');
    const statusDiv = document.getElementById('support-webhook-status');

    // Функция для отображения статуса
    function showStatus(message, type = 'info') {
        const alertClass = type === 'success' ? 'alert-success' :
                          type === 'error' ? 'alert-danger' : 'alert-info';
        statusDiv.innerHTML = `<div class="alert ${alertClass} alert-sm">${message}</div>`;
    }

    // Проверка webhook
    checkBtn.addEventListener('click', function() {
        checkBtn.disabled = true;
        checkBtn.textContent = '⏳';
        showStatus('Проверяем webhook бота поддержки...', 'info');

        fetch('fix_support_webhook.php?action=info')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    let message = `<strong>Бот:</strong> @${data.current_settings.bot_username}<br>`;
                    message += `<strong>Настроенный URL:</strong> ${data.current_settings.webhook_url}<br>`;

                    if (data.webhook_info && data.webhook_info.ok) {
                        const webhookResult = data.webhook_info.result;
                        message += `<strong>Текущий URL в Telegram:</strong> ${webhookResult.url || 'Не установлен'}<br>`;
                        message += `<strong>Ожидающих обновлений:</strong> ${webhookResult.pending_update_count || 0}<br>`;

                        if (data.needs_update) {
                            message += `<br><span class="text-warning">⚠️ Webhook требует обновления!</span>`;
                            showStatus(message, 'error');
                        } else {
                            message += `<br><span class="text-success">✅ Webhook настроен корректно</span>`;
                            showStatus(message, 'success');
                        }
                    } else {
                        message += `<br><span class="text-danger">❌ Ошибка получения информации о webhook</span>`;
                        showStatus(message, 'error');
                    }
                } else {
                    showStatus(`❌ Ошибка: ${data.error}`, 'error');
                }
            })
            .catch(error => {
                console.error('Check error:', error);
                showStatus('❌ Ошибка при проверке webhook', 'error');
            })
            .finally(() => {
                checkBtn.disabled = false;
                checkBtn.textContent = '🔍';
            });
    });

    // Исправление webhook
    fixBtn.addEventListener('click', function() {
        if (!confirm('Обновить webhook бота поддержки на основе текущих настроек?')) {
            return;
        }

        fixBtn.disabled = true;
        fixBtn.textContent = '⏳';
        showStatus('Обновляем webhook бота поддержки...', 'info');

        fetch('fix_support_webhook.php?action=update')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    let message = `✅ ${data.message}<br>`;
                    message += `<strong>URL:</strong> ${data.webhook_url}<br>`;
                    message += `<strong>Бот:</strong> @${data.bot_username}`;
                    showStatus(message, 'success');
                } else {
                    showStatus(`❌ Ошибка: ${data.error}`, 'error');
                }
            })
            .catch(error => {
                console.error('Fix error:', error);
                showStatus('❌ Ошибка при обновлении webhook', 'error');
            })
            .finally(() => {
                fixBtn.disabled = false;
                fixBtn.textContent = '🔧';
            });
    });

    // Автоматическая проверка при загрузке страницы
    setTimeout(() => {
        checkBtn.click();
    }, 1000);

    // === АВТОМАТИЧЕСКОЕ ОБНОВЛЕНИЕ URL ===
    const detectBtn = document.getElementById('detect-folder');
    const autoUpdateBtn = document.getElementById('auto-update-urls');
    const autoUpdateStatus = document.getElementById('auto-update-status');

    function showAutoUpdateStatus(message, type = 'info') {
        const alertClass = type === 'success' ? 'alert-success' :
                          type === 'error' ? 'alert-danger' : 'alert-info';
        autoUpdateStatus.innerHTML = `<div class="alert ${alertClass} alert-sm">${message}</div>`;
    }

    // Определение текущей папки
    detectBtn.addEventListener('click', function() {
        detectBtn.disabled = true;
        detectBtn.textContent = '⏳ Определяем...';

        fetch('auto_update_urls.php?action=detect')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    let message = `<strong>Определенная папка:</strong> ${data.detected_folder}<br><br>`;
                    message += '<strong>Текущие настройки:</strong><br>';
                    message += `WEBAPP_URL: ${data.current_settings.WEBAPP_URL}<br>`;
                    message += `WEBHOOK_URL: ${data.current_settings.WEBHOOK_URL}<br>`;
                    message += `SUPPORT_WEBHOOK_URL: ${data.current_settings.SUPPORT_WEBHOOK_URL}<br><br>`;
                    message += '<strong>Предлагаемые настройки:</strong><br>';
                    message += `WEBAPP_URL: ${data.suggested_settings.WEBAPP_URL}<br>`;
                    message += `WEBHOOK_URL: ${data.suggested_settings.WEBHOOK_URL}<br>`;
                    message += `SUPPORT_WEBHOOK_URL: ${data.suggested_settings.SUPPORT_WEBHOOK_URL}`;

                    showAutoUpdateStatus(message, 'info');
                } else {
                    showAutoUpdateStatus(`❌ Ошибка: ${data.error}`, 'error');
                }
            })
            .catch(error => {
                console.error('Detect error:', error);
                showAutoUpdateStatus('❌ Ошибка при определении папки', 'error');
            })
            .finally(() => {
                detectBtn.disabled = false;
                detectBtn.textContent = '🔍 Определить текущую папку';
            });
    });

    // Автоматическое обновление URL
    autoUpdateBtn.addEventListener('click', function() {
        if (!confirm('Автоматически обновить все URL на основе текущей папки проекта?\n\nЭто обновит:\n- WEBAPP_URL\n- WEBHOOK_URL\n- SUPPORT_WEBHOOK_URL\n\nА также установит webhook для обоих ботов.')) {
            return;
        }

        autoUpdateBtn.disabled = true;
        autoUpdateBtn.textContent = '⏳ Обновляем...';
        showAutoUpdateStatus('Обновляем URL и webhook...', 'info');

        fetch('auto_update_urls.php?action=update')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    let message = `✅ ${data.message}<br><br>`;
                    message += `<strong>Папка:</strong> ${data.folder}<br><br>`;
                    message += '<strong>Обновленные настройки:</strong><br>';
                    message += `WEBAPP_URL: ${data.updated_settings.WEBAPP_URL}<br>`;
                    message += `WEBHOOK_URL: ${data.updated_settings.WEBHOOK_URL}<br>`;
                    message += `SUPPORT_WEBHOOK_URL: ${data.updated_settings.SUPPORT_WEBHOOK_URL}<br><br>`;

                    message += '<strong>Результаты обновления webhook:</strong><br>';
                    if (data.webhook_results.main_bot) {
                        const mainResult = data.webhook_results.main_bot.ok ? '✅' : '❌';
                        message += `Основной бот: ${mainResult}<br>`;
                    }
                    if (data.webhook_results.support_bot) {
                        const supportResult = data.webhook_results.support_bot.ok ? '✅' : '❌';
                        message += `Бот поддержки: ${supportResult}<br>`;
                    }

                    message += '<br><em>Обновите страницу для применения изменений.</em>';

                    showAutoUpdateStatus(message, 'success');

                    // Обновляем поля формы
                    document.getElementById('cfg_WEBAPP_URL').value = data.updated_settings.WEBAPP_URL;
                    document.getElementById('cfg_WEBHOOK_URL').value = data.updated_settings.WEBHOOK_URL;
                    document.getElementById('cfg_SUPPORT_WEBHOOK_URL').value = data.updated_settings.SUPPORT_WEBHOOK_URL;

                } else {
                    showAutoUpdateStatus(`❌ Ошибка: ${data.error}`, 'error');
                }
            })
            .catch(error => {
                console.error('Auto update error:', error);
                showAutoUpdateStatus('❌ Ошибка при автоматическом обновлении', 'error');
            })
            .finally(() => {
                autoUpdateBtn.disabled = false;
                autoUpdateBtn.textContent = '🚀 Обновить все URL автоматически';
            });
    });
});
</script>
